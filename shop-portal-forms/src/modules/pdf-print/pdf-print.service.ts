import { Injectable, InternalServerErrorException } from '@nestjs/common'
import { isAxiosError, type AxiosInstance } from 'axios'
import FormData from 'form-data'
import { InjectHttp } from '../http/http-module'

type PrintOptions = { attributes?: Record<`data-${string}`, string> }

function processHtml(html: string, options: PrintOptions) {
  const { attributes = [] } = options

  function setAttributes(el: HTMLRewriterTypes.Element) {
    for (const [key, value] of Object.entries(attributes)) {
      if (value) el.setAttribute(key, value)
    }
  }

  function removeElement(el: HTMLRewriterTypes.Element) {
    el.remove()
  }

  return new HTMLRewriter()
    .on('html', { element: setAttributes })
    .on('script', { element: removeElement })
    .transform(html)
}

@Injectable()
export class PDFPrintService {
  constructor(@InjectHttp('PDF_PRINT') private readonly http: AxiosInstance) {}

  async print(html: string, options: PrintOptions): Promise<Buffer> {
    const data = new FormData()

    data.append('files', processHtml(html, options), { filename: 'index.html' })

    try {
      const response = await this.http.post<Buffer>('html', data, {
        headers: data.getHeaders(),
        responseType: 'arraybuffer',
      })

      return response.data
    } catch (e) {
      if (isAxiosError(e)) {
        throw new InternalServerErrorException(e.toJSON())
      } else {
        throw e
      }
    }
  }
}
