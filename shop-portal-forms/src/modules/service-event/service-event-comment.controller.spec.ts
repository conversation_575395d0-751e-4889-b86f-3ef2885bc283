import { useAppSandbox } from 'test/use-app-sandbox'
import { FormStatusCode } from '../form/model/form-status-code'
import { RequiredFormCommentDTO } from './dto/required-form-comment.dto'
import { RequiredFormComment } from './model/required-form-comment.entity'
import { RequiredForm } from './model/required-form.entity'
import { ServiceEvent } from './model/service-event.entity'

describe('ServiceEventCommentController', () => {
  const { get, getRepository } = useAppSandbox()

  async function seedRequiredForm({
    statusCode = FormStatusCode.IN_PROCESS,
    comments = [],
  }: {
    statusCode?: FormStatusCode
    comments?: Omit<RequiredFormCommentDTO, 'requiredFormId'>[]
  } = {}) {
    const serviceEvent = await getRepository(ServiceEvent).save({
      id: '123',
    })

    const form = await getRepository(RequiredForm).save({
      id: '1',
      serviceEventId: serviceEvent.id,
      dciFormId: 1,
      createdByLoginId: 'user-id',
      lastModifiedByLoginId: 'user-id',
      statusCode,
    })

    await getRepository(RequiredFormComment).save(
      comments.map((comment) => ({
        ...comment,
        requiredFormId: form.id,
      })),
    )

    return getRepository(RequiredForm).findOneOrFail({
      where: { id: form.id },
      relations: { comments: true },
    })
  }

  describe('GET /service-events/:id/comments', () => {
    it('returns 200 and the required form comments', async () => {
      const { http } = get()

      const form = await seedRequiredForm()

      await getRepository(RequiredFormComment).save([
        {
          content: 'This is a comment',
          requiredFormId: '1',
          createdByLoginId: 'user-id',
          createdDate: new Date(),
        },
        {
          content: 'This is a second comment',
          requiredFormId: '1',
          createdByLoginId: 'user-id',
          createdDate: new Date(),
        },
      ])

      const response = await http
        .get(`/service-events/${form.serviceEventId}/comments`)
        .expect(200)

      expect(response.body).toMatchObject([
        {
          id: '1',
          content: 'This is a comment',
          requiredFormId: '1',
          createdByLoginId: 'user-id',
        },
        {
          id: '2',
          content: 'This is a second comment',
          requiredFormId: '1',
          createdByLoginId: 'user-id',
        },
      ])
    })

    it('returns 200 and an empty list when the service event does not exist', async () => {
      const { http } = get()

      const response = await http
        .get(`/service-events/123/comments`)
        .expect(200)

      expect(response.body).toEqual([])
    })
  })

  describe('POST /service-events/:id/comments', () => {
    it.each([FormStatusCode.NOT_STARTED, FormStatusCode.IN_PROCESS])(
      'returns 200 and creates a comment for a form with status code %s',
      async (statusCode: FormStatusCode) => {
        const { http } = get()

        const form = await seedRequiredForm({ statusCode })

        const comment = {
          content: 'This is a comment',
          requiredFormId: '1',
          createdByLoginId: 'user-id',
        }

        await http
          .post(`/service-events/${form.serviceEventId}/comments`)
          .send(comment)
          .expect(200)

        const comments = await getRepository(RequiredFormComment).find({
          where: { requiredFormId: form.id },
        })

        expect(comments).toEqual([expect.objectContaining(comment)])
      },
    )

    it.each([
      FormStatusCode.ARCHIVED,
      FormStatusCode.DEACTIVATED,
      FormStatusCode.SIGNED,
    ])(
      'returns 422 when creating a comment for a form with status code %s',
      async (statusCode: FormStatusCode) => {
        const { http } = get()

        const form = await seedRequiredForm({ statusCode })

        const expected: Partial<RequiredFormComment> = {
          content: 'This is a comment',
          requiredFormId: '1',
          createdByLoginId: 'user-id',
        }

        await http
          .post(`/service-events/${form.serviceEventId}/comments`)
          .send(expected)
          .expect(422)
      },
    )

    it('returns 404 status when service event does not exist', async () => {
      const { http } = get()

      await http
        .post(`/service-events/123/comments`)
        .send({
          content: 'This is a comment',
          requiredFormId: '1',
          createdByLoginId: 'user-id',
        })
        .expect(404)
    })

    it('returns 404 when the required form id does not exist', async () => {
      const { http } = get()

      const serviceEvent = await getRepository(ServiceEvent).save({
        id: '123',
      })

      await http
        .post(`/service-events/${serviceEvent.id}/comments`)
        .send({
          content: 'This is a comment',
          requiredFormId: '1',
          createdByLoginId: 'user-id',
        })
        .expect(404)
    })

    it('returns 400 when the', async () => {
      const { http } = get()

      const form = await seedRequiredForm()

      await http
        .post(`/service-events/${form.serviceEventId}/comments`)
        .send({
          content: 'This is a comment',
          createdByLoginId: 'user-id',
        })
        .expect(400)
    })

    it('returns 400 when the service event id is not a number', async () => {
      const { http } = get()

      const response = await http
        .get('/service-events/abc/comments')
        .expect(400)

      expect(response.body).toHaveProperty('message')
    })
  })

  describe.only('DELETE /service-events/:id/comments/:commentId', () => {
    it.each([FormStatusCode.NOT_STARTED, FormStatusCode.IN_PROCESS])(
      'returns 200 and soft deletes a comment of a form with status code %s',
      async (statusCode: FormStatusCode) => {
        const { http } = get()

        const form = await seedRequiredForm({
          statusCode,
          comments: [
            { content: 'This is a comment', createdByLoginId: 'user-id' },
            { content: 'This is another comment', createdByLoginId: 'user-id' },
          ],
        })

        const [comment1] = form.comments

        await http
          .delete(
            `/service-events/${form.serviceEventId}/comments/${comment1.id}`,
          )
          .expect(200)

        const comments = await getRepository(RequiredFormComment).find({
          where: { requiredFormId: form.id },
        })

        expect(comments).toMatchObject([
          {
            content: 'This is a comment',
            createdByLoginId: 'user-id',
            deletedDate: expect.any(Date) as unknown,
          },
          {
            content: 'This is another comment',
            createdByLoginId: 'user-id',
            deletedDate: null,
          },
        ])
      },
    )

    it.each([
      FormStatusCode.ARCHIVED,
      FormStatusCode.DEACTIVATED,
      FormStatusCode.SIGNED,
    ])(
      'returns 422 when soft deleting a comment of a form with status code %s',
      async (statusCode: FormStatusCode) => {
        const { http } = get()

        const form = await seedRequiredForm({
          statusCode,
          comments: [
            { content: 'This is a comment', createdByLoginId: 'user-id' },
            { content: 'This is another comment', createdByLoginId: 'user-id' },
          ],
        })

        const [comment1] = form.comments

        await http
          .delete(
            `/service-events/${form.serviceEventId}/comments/${comment1.id}`,
          )
          .expect(422)
      },
    )
  })
})
