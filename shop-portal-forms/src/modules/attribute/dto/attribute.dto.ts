import { ApiProperty } from '@nestjs/swagger'
import { IsString } from 'class-validator'

export class AttributeDTO {
  @ApiProperty({
    description: `The id of the attribute.`,
    example: 34,
  })
  @IsString()
  id: string

  @ApiProperty({
    description: `The code indicating which zone this attribute is in.`,
    example: 'LC',
  })
  @IsString()
  zoneLocationCode: string

  @ApiProperty({
    description: `The codes to use for locating the correct mechanical record.`,
    example: 'LC',
  })
  @IsString({ each: true })
  mrLocationCode: string[]
}
