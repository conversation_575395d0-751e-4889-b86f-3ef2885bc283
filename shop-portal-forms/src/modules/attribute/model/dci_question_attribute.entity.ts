import { DCIQuestion } from 'src/modules/dci/model/dci-question.entity'
import {
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  type Relation,
} from 'typeorm'
import { AttributeZoneXref } from './attribute-zone-xref.entity'

@Entity('dci_question_attribute')
export class DCIQuestionAttribute {
  @PrimaryColumn({
    name: 'dci_question_id',
    type: 'integer',
    nullable: false,
  })
  @Index('dci_question_attribute_fk1')
  dciQuestionId: number

  @PrimaryColumn({
    name: 'attribute_id',
    type: 'bigint',
    comment: 'The Portal Category used in the Shop Car Spec Sheet view.',
    nullable: false,
  })
  attributeId: number

  @OneToMany(
    'AttributeZoneXref',
    (attrZoneRef: AttributeZoneXref) => attrZoneRef.dciQuestionAttribute,
  )
  attributeZoneXref: Relation<AttributeZoneXref[]>

  @ManyToOne(() => DCIQuestion, (question) => question.dciQuestionAttribute, {
    onDelete: 'NO ACTION',
    deferrable: 'INITIALLY DEFERRED',
  })
  @JoinColumn({
    name: 'dci_question_id',
    foreignKeyConstraintName: 'dciquestion_2_dciquesattrib_c1',
  })
  question: DCIQuestion
}
