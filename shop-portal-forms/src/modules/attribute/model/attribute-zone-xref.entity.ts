import {
  Column,
  Entity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryColumn,
} from 'typeorm'
import { DCIQuestionAttribute } from './dci_question_attribute.entity'
import { ZoneLocation } from 'src/modules/location/model/zone-location.entity'

@Entity({
  name: 'attribute_zone_xref',
  comment: 'This table was added for story 8316 for CR 71463.',
})
export class AttributeZoneXref {
  @PrimaryColumn({
    name: 'attribute_id',
    type: 'bigint',
    comment: 'The Portal Category used in the Shop Car Spec Sheet view.',
  })
  attributeId: string

  @PrimaryColumn({
    name: 'mr_locn_code',
    type: 'character varying',
    length: 20,
  })
  mrLocationCode: string

  @Column({
    name: 'zone_locn_code',
    type: 'character varying',
    length: 2,
    comment:
      'A location on the railcar used to segment the Inbound Inspection process to use multiple forms. (Inbound Inpsection form is the only form using the Zone Location Code at this point in time.)',
    nullable: true,
  })
  @Index('attribute_zone_xref_fk1')
  zoneLocationCode: string

  @ManyToOne(() => DCIQuestionAttribute, (attr) => attr.attributeZoneXref, {
    onDelete: 'SET NULL',
    deferrable: 'INITIALLY DEFERRED',
    createForeignKeyConstraints: false,
  })
  @JoinColumn({
    name: 'attribute_id',
  })
  dciQuestionAttribute: DCIQuestionAttribute

  @ManyToOne(() => ZoneLocation, (zl) => zl.attributeZoneXrefs, {
    onDelete: 'SET NULL',
    deferrable: 'INITIALLY DEFERRED',
  })
  @JoinColumn({
    name: 'zone_locn_code',
    foreignKeyConstraintName: 'zonelocn_2_attribzonexref_con1',
  })
  zoneLocation: ZoneLocation
}
