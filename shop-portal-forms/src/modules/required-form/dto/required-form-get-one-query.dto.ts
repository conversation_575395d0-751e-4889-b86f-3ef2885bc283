import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsOptional } from 'class-validator'

export enum FormPrintType {
  BLANK = 'blank',
  FILLED = 'filled',
  CUSTOMER = 'customer',
}

export class RequiredFormGetOneQueryDTO {
  @ApiProperty({
    name: 'printType',
    type: 'string',
    description: `The type of print to return. Ignored if a pdf version of the form is not requestes.`,
    example: 'blank',
  })
  @IsEnum(FormPrintType)
  @IsOptional()
  printType: FormPrintType = FormPrintType.FILLED
}
