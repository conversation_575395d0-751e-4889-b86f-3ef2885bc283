import { ApiProperty } from '@nestjs/swagger'
import { FormSignatureHistoryEntryType } from '../model/required-form-content.entity'

export class RequiredFormSignatureHistoryDTO {
  @ApiProperty({
    description: `The action performed on the form section`,
    example: 'complete',
  })
  action: FormSignatureHistoryEntryType;

  // Dynamic properties based on extraction keys
  // For example: performedBy, approvedBy, signedBy, etc.
  // And their corresponding date fields: performedAt, approvedDate, signedAt, etc.
  [key: string]: any
}
