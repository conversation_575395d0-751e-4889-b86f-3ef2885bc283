import { ApiProperty } from '@nestjs/swagger'
import {
  IsDate,
  IsNumber,
  IsObject,
  IsOptional,
  ValidateNested,
} from 'class-validator'
import { RequiredFormSignatureHistoryDTO } from './required-form-signature-history.dto'
import { RequiredFormQuestionResponseDTO } from './required-form-question-response.dto'
import { RequiredFormSignatureDTO } from './required-form-signature.dto'

export class RequiredFormContentDTO {
  @ApiProperty({
    description: `The question responses of the form.`,
    type: RequiredFormQuestionResponseDTO,
  })
  @IsObject()
  @ValidateNested()
  response: Record<string, RequiredFormQuestionResponseDTO>

  @ApiProperty({
    description: `The signatures related to the form.`,
    type: RequiredFormSignatureDTO,
  })
  @IsObject()
  @ValidateNested()
  signatures: Record<string, RequiredFormSignatureDTO>

  @ApiProperty({
    description: `The signature history for form signature actions.`,
    type: RequiredFormSignatureHistoryDTO,
    isArray: true,
  })
  @IsObject()
  @ValidateNested()
  signatureHistory: Record<string, RequiredFormSignatureHistoryDTO[]>

  @ApiProperty({
    description: `The version of the content. Used for optimistic concurrency control.`,
    type: 'number',
  })
  @IsNumber()
  version: number

  @ApiProperty({
    description: 'The date the associated required form was created',
    example: '2023-10-01T12:34:56Z',
  })
  @IsDate()
  @IsOptional()
  createdDate: Date

  @ApiProperty({
    description: 'The date the associated required form was last modified',
    example: '2023-10-01T12:34:56Z',
  })
  @IsDate()
  @IsOptional()
  lastModifiedDate: Date
}
