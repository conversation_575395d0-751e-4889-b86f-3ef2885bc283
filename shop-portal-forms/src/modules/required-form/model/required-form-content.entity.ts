import { validateOrReject } from 'class-validator'
import { RequiredForm } from 'src/modules/service-event/model/required-form.entity'
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
} from 'typeorm'
import { z } from 'zod'
import { RequiredFormContentDTO } from '../dto/required-form-content.dto'
import { RequiredFormSignatureDTO } from '../dto/required-form-signature.dto'
import { RequiredFormSignatureHistoryDTO } from '../dto/required-form-signature-history.dto'

const FormSignatureType = z.enum(['complete', 'deferred'])
const FormSignatureHistoryEntryType = z.union([
  FormSignatureType,
  z.enum(['unsign']),
])

const RequiredFormContentData = z.object({
  formResponses: z.object({
    response: z.record(
      z.object({
        questionId: z.number(),
        responseId: z.number(),
        data: z.union([z.record(z.any()), z.record(z.any()).array()]),
      }),
    ),
    signatures: z.record(
      z.object({
        signedBy: z.object({
          id: z.string(),
          name: z.string(),
        }),
        signedAt: z.string(),
        extractionKey: z.string().default('signedBy'),
        extractionKeyDateLabel: z.string().default('signedAt'),
        type: FormSignatureType,
      }),
    ),
    signatureHistory: z
      .record(
        z.array(
          z.record(z.any()).and(
            z.object({
              action: FormSignatureHistoryEntryType,
            }),
          ),
        ),
      )
      .default({}),
  }),
})

type RequiredFormContentData = z.infer<typeof RequiredFormContentData>
type FormSignatureType = z.infer<typeof FormSignatureType>
type FormSignatureHistoryEntryType = z.infer<
  typeof FormSignatureHistoryEntryType
>

@Entity()
@Index('required_form_content_fk1', ['requiredFormId'])
export class RequiredFormContent {
  @PrimaryColumn({
    name: 'required_form_content_id',
    type: 'bigint',
    nullable: false,
    generated: 'increment',
  })
  id: string

  @Column({
    name: 'form_content',
    comment: `The Form Content table contains the XML for the form.`,
    type: 'text',
    nullable: false,
  })
  formXmlExport: string

  @Column({
    name: 'required_form_id',
    comment: `A unique key for the Form Content table, which is automatically generated by the database.`,
    type: 'bigint',
    nullable: false,
  })
  requiredFormId: string

  @Column({
    name: 'row_version_num',
    comment: `Used to support optimistic locking. Check is implemented via trigger.`,
    type: 'integer',
    nullable: false,
    default: 0,
  })
  rowVersionNum: number

  @Column({
    name: 'form_responses',
    type: 'jsonb',
    nullable: true,
  })
  formResponses: RequiredFormContentData['formResponses'] | null

  @Column({
    name: 'created_date',
    comment: `The date on which the form content was created.`,
    type: 'timestamp without time zone',
    precision: 0,
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdDate: Date

  @Column({
    name: 'created_by_login_id',
    comment: `The login id of the person who created the form content.`,
    type: 'character varying',
    length: 30,
    nullable: false,
  })
  createdByLoginId: string

  @Column({
    name: 'last_modified_date',
    comment: `The date on which the form content was last modified.`,
    type: 'timestamp without time zone',
    precision: 0,
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
  })
  lastModifiedDate: Date

  @Column({
    name: 'last_modified_by_login_id',
    comment: `The login id of the person who last modified the form content.`,
    type: 'character varying',
    length: 30,
    nullable: false,
  })
  lastModifiedByLoginId: string

  @ManyToOne(() => RequiredForm, (requiredForm) => requiredForm.contents, {
    onDelete: 'CASCADE',
    onUpdate: 'NO ACTION',
    deferrable: 'INITIALLY DEFERRED',
  })
  @JoinColumn({
    name: 'required_form_id',
    foreignKeyConstraintName: 'rqrdfrm_to_rqrfrmcntnt_constr1',
  })
  requiredForm: RequiredForm

  async toDTO(): Promise<RequiredFormContentDTO> {
    const dto = new RequiredFormContentDTO()
    if (this.formResponses) {
      dto.response = this.formResponses.response
      dto.signatures = this.formResponses.signatures as Record<
        string,
        RequiredFormSignatureDTO
      >
      dto.signatureHistory = this.formResponses
        .signatureHistory as unknown as Record<
        string,
        RequiredFormSignatureHistoryDTO[]
      >
    }

    dto.version = this.rowVersionNum
    dto.createdDate = this.createdDate
    dto.lastModifiedDate = this.lastModifiedDate

    await validateOrReject(dto)
    return dto
  }
}

export {
  RequiredFormContentData,
  FormSignatureType,
  FormSignatureHistoryEntryType,
}
