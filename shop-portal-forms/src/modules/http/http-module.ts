import { FactoryProvider, Inject, Module } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import type { AxiosInstance } from 'axios'
import Axios from 'axios'

const HTTP_CLIENT_TYPES = ['PDF_PRINT'] as const

type HttpClientType = (typeof HTTP_CLIENT_TYPES)[number]

const URL_ENV_KEYS: Record<HttpClientType, string> = {
  PDF_PRINT: 'PDF_PRINT_API_ROOT',
}

function httpClientToken(type: HttpClientType) {
  return `${type}_AXIOS_INSTANCE`
}

function registerHttpClient(
  type: HttpClientType,
): FactoryProvider<AxiosInstance> {
  return {
    provide: httpClientToken(type),
    useFactory: (config: ConfigService) => {
      return Axios.create({ baseURL: config.get<string>(URL_ENV_KEYS[type]) })
    },
    inject: [ConfigService],
  }
}

@Module({
  imports: [],
  providers: HTTP_CLIENT_TYPES.map(registerHttpClient),
  exports: HTTP_CLIENT_TYPES.map(httpClientToken),
})
export class HttpModule {}

export function InjectHttp(type: HttpClientType) {
  return Inject(httpClientToken(type))
}

export { HTTP_CLIENT_TYPES, httpClientToken }
export type { HttpClientType }
