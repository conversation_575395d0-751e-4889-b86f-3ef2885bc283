import { ConfigModule, ConfigService } from '@nestjs/config'
import { Test, TestingModule } from '@nestjs/testing'
import type { AxiosInstance } from 'axios'
import {
  HTTP_CLIENT_TYPES,
  httpClientToken,
  HttpClientType,
  HttpModule,
} from './http-module'

const MOCK_ENV = {
  PDF_PRINT_API_ROOT: 'http://pdfprint.gatx.com',
}

const AXIOS_BASE_URLS: Record<HttpClientType, keyof typeof MOCK_ENV> = {
  PDF_PRINT: 'PDF_PRINT_API_ROOT',
}

describe('HttpModule', () => {
  let module: TestingModule

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ load: [() => MOCK_ENV], isGlobal: true }),
        HttpModule,
      ],
    })
      .overrideProvider(ConfigService)
      .useValue({
        get: (key: string) => MOCK_ENV[key as keyof typeof MOCK_ENV],
      })
      .compile()
  })

  it.each(HTTP_CLIENT_TYPES)(
    'the %s http service should have the correct base URL',
    (type) => {
      const http = module.get<AxiosInstance>(httpClientToken(type))

      expect(http.defaults.baseURL).toBe(MOCK_ENV[AXIOS_BASE_URLS[type]])
    },
  )
})
