import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  OneToMany,
  PrimaryGeneratedColumn,
  type Relation,
} from 'typeorm'
import type { DCIForm } from './dci-form.entity'

@Entity({
  name: 'dci_form_group',
  comment: 'A DCI Form Group represents a set of related DCI Forms',
})
export class DCIFormGroup {
  @PrimaryGeneratedColumn({
    name: 'dci_form_group_id',
    comment: `A unique key for the form group table.`,
    type: 'integer',
  })
  id: number

  @Column({
    name: 'description',
    comment: 'A description of the form group',
    type: 'character varying',
    length: 200,
  })
  description: string

  @OneToMany('DCIForm', (dciForm: DCIForm) => dciForm.group)
  forms: Relation<DCIForm[]>
}
