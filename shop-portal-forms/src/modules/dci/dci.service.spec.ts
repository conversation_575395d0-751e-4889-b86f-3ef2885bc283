import { Repository } from 'typeorm'
import { useAppSandbox } from '../../../test/use-app-sandbox'
import { RequiredForm } from '../service-event/model/required-form.entity'
import { DCIService } from './dci.service'
import { OrchestrationRequestDTO } from './dto/orchestration-request.dto'

describe('DCIService', () => {
  const { resolveInstance, getRepository } = useAppSandbox()

  let service: DCIService
  let requiredForms: Repository<RequiredForm>

  beforeEach(async () => {
    service = await resolveInstance(DCIService)
    requiredForms = getRepository(RequiredForm)
  })

  describe('initiateOrchestration', () => {
    it('orchestrates form creation for a service event', async () => {
      const orchestrationData: OrchestrationRequestDTO = {
        serviceEvent: {
          id: '245',
          confirmedStccCode: 'string',
          confirmedStccName: 'string',
          confirmedStccCorrosiveIndicator: 'N',
          futureStccCode: 'string',
          futureStccName: 'string',
          futureStccCorrosiveIndicator: 'N',
        },
        programs: [
          {
            code: 'P1',
            number: '123',
            revision: 'B',
          },
        ],
        dciQuestions: [{ id: '123' }],
      }

      await service.initiateOrchestration(orchestrationData, 'ADMIN')

      expect(await requiredForms.find()).toHaveLength(2)
    })

    it('orchestrates more complex form creation for a service event', async () => {
      const orchestrationData: OrchestrationRequestDTO = {
        serviceEvent: {
          id: '5125047',
          confirmedStccCode: '2812534',
          confirmedStccName: 'POTASSIUM CHLORIDE',
          confirmedStccCorrosiveIndicator: 'Y',
          futureStccCode: '2812534',
          futureStccName: 'POTASSIUM CHLORIDE',
          futureStccCorrosiveIndicator: 'Y',
        },
        programs: [
          {
            code: 'EN',
            number: '686',
            revision: ' ',
          },
          {
            code: 'EN',
            number: '779',
            revision: ' ',
          },
          {
            code: 'XX',
            number: '5000000',
            revision: ' ',
          },
          {
            code: 'BT',
            number: '1000',
            revision: ' ',
          },
        ],
        dciQuestions: [
          { id: '101' },
          { id: '102' },
          { id: '103' },
          { id: '104' },
          { id: '283' },
          { id: '2072' },
          { id: '2073' },
          { id: '2074' },
          { id: '2075' },
          { id: '2076' },
          { id: '2077' },
          { id: '2078' },
          { id: '2079' },
          { id: '2080' },
          { id: '2189' },
          { id: '2197' },
          { id: '2192' },
          { id: '2193' },
          { id: '2202' },
          { id: '2791' },
          { id: '2794' },
          { id: '2796' },
          { id: '2799' },
          { id: '2800' },
          { id: '2801' },
          { id: '2802' },
          { id: '2803' },
          { id: '2804' },
          { id: '2805' },
          { id: '2806' },
          { id: '2807' },
          { id: '2811' },
          { id: '2815' },
          { id: '2817' },
          { id: '2818' },
          { id: '2823' },
          { id: '2824' },
          { id: '2825' },
          { id: '2826' },
          { id: '2827' },
          { id: '2828' },
          { id: '2830' },
          { id: '2833' },
          { id: '2837' },
          { id: '2841' },
          { id: '2842' },
          { id: '2843' },
          { id: '2844' },
          { id: '2847' },
          { id: '2849' },
          { id: '2821' },
          { id: '2868' },
          { id: '2832' },
          { id: '2838' },
          { id: '2839' },
          { id: '2848' },
          { id: '2852' },
          { id: '2853' },
          { id: '2813' },
          { id: '2845' },
          { id: '2820' },
          { id: '2785' },
          { id: '2875' },
          { id: '2883' },
          { id: '2884' },
          { id: '2885' },
          { id: '2886' },
          { id: '2896' },
          { id: '2897' },
          { id: '2903' },
          { id: '2904' },
          { id: '2905' },
          { id: '2906' },
          { id: '2909' },
          { id: '2912' },
          { id: '2916' },
          { id: '2917' },
          { id: '2196' },
          { id: '2922' },
          { id: '606' },
          { id: '2924' },
          { id: '2927' },
          { id: '2929' },
          { id: '2930' },
          { id: '2931' },
          { id: '2932' },
          { id: '782' },
          { id: '1903' },
          { id: '315' },
          { id: '576' },
          { id: '647' },
          { id: '649' },
          { id: '650' },
          { id: '654' },
          { id: '655' },
          { id: '656' },
          { id: '660' },
          { id: '662' },
          { id: '664' },
          { id: '653' },
          { id: '651' },
          { id: '652' },
          { id: '2180' },
          { id: '2181' },
          { id: '2778' },
          { id: '2779' },
          { id: '663' },
          { id: '665' },
          { id: '668' },
          { id: '2102' },
          { id: '2104' },
          { id: '2066' },
          {
            id: '2154',
            programs: [
              {
                code: 'EN',
                number: '686',
                revision: ' ',
              },
              {
                code: 'EN',
                number: '779',
                revision: ' ',
              },
              {
                code: 'BT',
                number: '1000',
                revision: ' ',
              },
            ],
          },
          { id: '2887' },
          { id: '2798' },
          { id: '2810' },
          { id: '2814' },
          { id: '2831' },
          { id: '2836' },
        ],
      }

      await service.initiateOrchestration(orchestrationData, 'ADMIN')
      expect(await requiredForms.find()).toHaveLength(18)
    })
  })
})
