-- Generated by Ora2Pg, the Oracle database Schema converter, version 24.3
-- Copyright 2000-2024 <PERSON> DAROL<PERSON>. All rights reserved.
-- DATASOURCE: dbi:Oracle:host=**************;service_name=ORCLPDB1;port=1521

SET client_encoding TO 'UTF8';

CREATE OR REPLACE VIEW shop_seq_model_no_v (model_prefix, model_num_suffix, model_number) AS (
  SELECT DISTINCT MP.MODEL_NUM AS MODEL_PREFIX,
CASE WHEN MS.MODEL_NUM_SUFFIX='NONE' THEN  null  ELSE MS.MODEL_NUM_SUFFIX END  as MODEL_NUM_SUFFIX,
replace(MP.MODEL_NUM, '-', '') || replace(CASE WHEN MS.MODEL_NUM_SUFFIX='NONE' THEN  null  ELSE MS.MODEL_NUM_SUFFIX END , '-', '') AS MODEL_NUMBER
FROM DCI_FORM DF
INNER JOIN DCI ON DCI.DCI_FORM_ID = DF.DCI_FORM_ID
INNER JOIN DCI_RESPONSE_PATTERN_GROUP DCIRPG ON DCIRPG.DCI_QUESTION_ID = DCI.DCI_QUESTION_ID
INNER JOIN MANUFACTURER MANF ON MANF.DROPDOWN_SOURCE_ID = DCIRPG.DROPDOWN_SOURCE_ID
INNER JOIN MANUFACTURER_MODEL_NO MP ON MP.MFACT_ID = MANF.MANUFACTURER_ID
LEFT OUTER JOIN MODEL_NUMBER_SUFFIX MS ON (MP.MFACT_MODEL_NO_ID = MS.MFACT_MODEL_NO_ID)
WHERE
DF.DCI_FORM_ID IN (22, 27)
);

CREATE OR REPLACE VIEW addtl_shopping_instruc_msg_v (service_event_id, dci_question_id, reference_doc, form_short_name, dspl_in_shopping_instruc_flag) AS (
select
 se.service_event_id,
 dq.dci_question_id,
 rd.reference_doc,
 df.form_short_name,
 rd.dspl_in_shopping_instruc_flag
FROM service_event se
 inner join required_form rf on rf.service_event_id = se.service_event_id
 inner join required_dci rdci on rdci.required_form_id = rf.required_form_id
 inner join dci on dci.dci_id = rdci.dci_id and dci.dci_form_id = rf.dci_form_id
 inner join dci_question dq on dq.dci_question_id = dci.dci_question_id
 inner join reference_document rd on rd.dci_question_id = dq.dci_question_id and     rd.dspl_in_shopping_instruc_flag = 'Y'
 inner join dci_form df on df.dci_form_id = rf.dci_form_id
);

CREATE OR REPLACE VIEW service_equip_part_type_code_v (part_type_code) AS select distinct part_type_code FROM dci_question
where insp_method = 'Confirm Fittings'
and part_type_code is not null;

CREATE OR REPLACE VIEW shop_form_completion_v (service_event_id, form_short_name, last_modified_date, last_modified_by_login_id, outbound_date) AS (select
se.service_event_id,
df.form_short_name,
rf.last_modified_date,
rf.last_modified_by_login_id,
decal_upn_cmplt_as_of_otb_date as outbound_date
FROM
required_form rf
inner join service_event se on rf.service_event_id = se.service_event_id
inner join dci_form df on rf.dci_form_id = df.dci_form_id
where rf.form_status_code = 'CM'
);

CREATE OR REPLACE VIEW se_form_customer_report_v (service_event_id, required_form_id, form_short_name, cust_rpt_flag, cust_rpt_dstn_folder_name, required_dci_id, dci_id) AS (
 Select Se.Service_Event_Id, Rf.required_form_id, df.form_short_name, df.cust_rpt_flag, df.cust_rpt_dstn_folder_name,  rd.required_dci_id, rd.dci_id
 FROM Service_Event Se
 Inner Join Required_Form Rf On Rf.Service_Event_Id = Se.Service_Event_Id
 Inner Join Dci_Form Df On Df.Dci_Form_Id = Rf.Dci_Form_Id And Df.Cust_Rpt_Flag = 'Y'
 Inner Join Required_Dci rd on rd.required_form_id = Rf.required_form_id
 Where
 Se.Form_Finalized_Date Is Null
 );

CREATE OR REPLACE VIEW fc_prepop_seq_v (source_question_id, dci_question, insp_method, source_dci_id, dci_group_id, dest_dci_id, dest_insp_method, dest_question_id) AS (select q2.dci_question_id Source_question_id, q2.dci_question, q2.insp_method,d2.dci_id Source_Dci_ID,d.dci_group_id,
d.dci_id Dest_Dci_ID,q.insp_method dest_insp_method, q.dci_question_id Dest_question_id
FROM dci d,  (select dci_question,insp_method, max(dci_question_id) dci_question_id
            from dci_question
            where insp_method like '%Confirm Fittings%'
            group by dci_question,insp_method) q, dci_question q2, dci d2
WHERE d.dci_question_id = q.dci_question_id
and q.insp_method = 'Confirm Fittings'
and d2.dci_question_id = q2.dci_question_id
and q2.insp_method = 'ID Fittings'
and trim(both q.dci_question) = trim(both q2.dci_question)
and d.dci_group_id = d2.dci_group_id
);

CREATE OR REPLACE VIEW service_event_v (service_event_id, decal_lst_rpt_as_of_inbnd_date, decal_upn_cmplt_as_of_otb_date, form_finalized_by_login_id, form_finalized_date, confirmed_stcc_code, confirmed_stcc_corrosive_ind, future_stcc_code, future_stcc_corrosive_ind) AS (
    select service_event_id,
      decal_lst_rpt_as_of_inbnd_date,
      decal_upn_cmplt_as_of_otb_date,
      form_finalized_by_login_id,
      form_finalized_date,
      CONFIRMED_STCC_CODE,
      CONFIRMED_STCC_CORROSIVE_IND,
      FUTURE_STCC_CODE,
      FUTURE_STCC_CORROSIVE_IND
    FROM service_event
);

CREATE OR REPLACE VIEW model_no_v (model_prefix, model_num_suffix, model_number) AS (
select distinct MP.model_num as model_prefix, MS.model_num_suffix, replace(MP.model_num, '-', '') || replace(MS.model_num_suffix, '-', '') as model_number
FROM manufacturer_model_no MP
left outer join model_number_suffix MS on (MP.mfact_model_no_id = MS.mfact_model_no_id) and(MS.model_num_suffix <> 'NONE')
);

CREATE OR REPLACE VIEW underframe_inspection_point_v (dci_question_id, dropdown_source_id, inspection_point, reference_dropdown_source_id, dci_id, zone_locn_code, sequence_num, tcid_component_id) AS (
select distinct
dqds.DCI_QUESTION_ID,
dqds.DROPDOWN_SOURCE_ID,
dq.DCI_QUESTION || ' Figure ' || fig.figure || ' Item ' || fig.item as inspection_point,
drpg.dropdown_source_id as reference_dropdown_source_id,
dci.dci_id,
dci.zone_locn_code,
dci.sequence_num,
dq.tcid_component_id
FROM DCI_QUESTION_DROPDOWN_SOURCE dqds
left outer join dci_question dq on dqds.DCI_QUESTION_ID = dq.DCI_QUESTION_ID
left outer join reference_document rf on dqds.DCI_QUESTION_ID = rf.DCI_QUESTION_ID
left outer join figure fig on rf.reference_doc_id = fig.reference_doc_id
left outer join DCI_RESPONSE_PATTERN_GROUP drpg on dqds.DCI_QUESTION_ID = drpg.DCI_QUESTION_ID
left outer join dci on dci.dci_question_id = dqds.dci_question_id
where
drpg.response_option_id in (2,26)
);

CREATE OR REPLACE VIEW required_maintenance_form_v (service_event_id, form_name, form_section, maint_form_id, form_status, form_source, form_type_code, required_form_id, sequence_num, hold_finalize_flag, forms_validated_flag, auto_generated_flag, validate_flag, forms_validation_warning_flag, archived_flag, archiveable_flag) AS (
Select Rmf.Service_Event_Id,
       Dci.form_short_name As FORM_NAME,
       ' ' as FORM_SECTION,
       Rmf.required_form_id as MAINT_FORM_ID,
       Rmf.Form_Status_code as FORM_STATUS,
       ' ' as FORM_SOURCE,
       ' ' as FORM_TYPE_CODE,
      Rmf.Required_Form_Id,
       Dci.SEQUENCE_NUM,
       se.Hold_Finalize_Flag,
       se.Forms_Validated_Flag,
       Dci.Auto_Generated_Flag,
       Dci.Validate_Flag,
       se.FORMS_VALIDATION_WARNING_FLAG,
       Rmf.archived_flag,
       Dci.archiveable_flag
FROM Required_Form Rmf,
     Form_Template Ft,
      Dci_Form Dci,
      service_event se
where Rmf.dci_form_id = Dci.dci_form_id
   And Dci.Form_Template_Id = Ft.Form_Template_Id
   And Rmf.Service_Event_Id = se.Service_Event_Id
);

CREATE OR REPLACE VIEW dci_zone_v (dci_question_id, dci_id, zone_locn_code) AS (select dci_question_id,dci_id,zone_locn_code
FROM dci);

CREATE OR REPLACE VIEW tcid_value_v (gatx_desc_type, tcid_desc_type, tcid_value, dropdown_value, ring_qty) AS select td.gatx_desc_type, td.tcid_desc_type, td.tcid_value, tdc.defect_cause_name as dropdown_value, null
FROM tcid_description td,
tcid_defect_cause tdc
where td.tcid_desc_id = tdc.tcid_desc_id

UNION ALL

select td.gatx_desc_type, td.tcid_desc_type, td.tcid_value, tdt.tcid_defect_type_name as dropdown_value, null
from tcid_description td,
tcid_defect_type tdt
where td.tcid_desc_id = tdt.tcid_desc_id

UNION ALL

select td.gatx_desc_type, td.tcid_desc_type, td.tcid_value, tim.insp_method_name as dropdown_value, null
from tcid_description td,
tcid_inspection_method tim
where td.tcid_desc_id = tim.tcid_desc_id

UNION ALL

select td.gatx_desc_type, td.tcid_desc_type, td.tcid_value, trm.repair_method_name as dropdown_value, null
from tcid_description td,
tcid_repair_method trm
where td.tcid_desc_id = trm.tcid_desc_id

UNION ALL

select td.gatx_desc_type, td.tcid_desc_type, td.tcid_value, tl.locn_name as dropdown_value, null
from tcid_description td,
tcid_location tl
where td.tcid_desc_id = tl.tcid_desc_id;

