-- Generated by Ora2Pg, the Oracle database Schema converter, version 24.3
-- Copyright 2000-2024 Gilles DAROLD. All rights reserved.
-- DATASOURCE: dbi:Oracle:host=**************;service_name=ORCLPDB1;port=1521

SET client_encoding TO 'UTF8';
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE aar_style (
	style_id smallint NOT NULL,
	dropdown_source_id integer NOT NULL,
	aar_style_code varchar(20)
);

COMMENT ON TABLE aar_style IS E'AAR Style contain the codes for the AAR Style Codes for hinged and bolted manways.';
COMMENT ON COLUMN aar_style.aar_style_code IS E'AAR Style Code for hinged and bolted manways.';
COMMENT ON COLUMN aar_style.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
COMMENT ON COLUMN aar_style.style_id IS E'surrogate key';
ALTER TABLE aar_style ADD PRIMARY KEY (style_id);
ALTER TABLE aar_style ALTER COLUMN style_id SET NOT NULL;
ALTER TABLE aar_style ALTER COLUMN dropdown_source_id SET NOT NULL;


CREATE TABLE allowable_thickness_reduction (
	tank_test_pressure_min smallint NOT NULL,
	tank_test_pressure_max smallint NOT NULL,
	corrosive_commodity_flag varchar(1) NOT NULL,
	locn_group_id smallint NOT NULL,
	corrosion_type_id integer NOT NULL,
	allowable_thickness_reduction decimal(5,4) NOT NULL
) ;
COMMENT ON COLUMN allowable_thickness_reduction.locn_group_id IS E'A unique identifier for a Location Group.';
CREATE INDEX allowable_thickness_reduct_fk1 ON allowable_thickness_reduction (locn_group_id);
CREATE INDEX allowable_thickness_reduct_fk2 ON allowable_thickness_reduction (corrosion_type_id);
ALTER TABLE allowable_thickness_reduction ADD PRIMARY KEY (tank_test_pressure_min,tank_test_pressure_max,corrosive_commodity_flag,locn_group_id,corrosion_type_id);
ALTER TABLE allowable_thickness_reduction ALTER COLUMN tank_test_pressure_min SET NOT NULL;
ALTER TABLE allowable_thickness_reduction ALTER COLUMN tank_test_pressure_max SET NOT NULL;
ALTER TABLE allowable_thickness_reduction ALTER COLUMN corrosive_commodity_flag SET NOT NULL;
ALTER TABLE allowable_thickness_reduction ALTER COLUMN locn_group_id SET NOT NULL;
ALTER TABLE allowable_thickness_reduction ALTER COLUMN corrosion_type_id SET NOT NULL;
ALTER TABLE allowable_thickness_reduction ALTER COLUMN allowable_thickness_reduction SET NOT NULL;


CREATE TABLE attribute_zone_xref (
	attribute_id bigint NOT NULL,
	mr_locn_code varchar(20) NOT NULL,
	zone_locn_code varchar(2)
) ;
COMMENT ON TABLE attribute_zone_xref IS E'This table was added for story 8316 for CR 71463.';
COMMENT ON COLUMN attribute_zone_xref.attribute_id IS E'The Portal Category used in the Shop Car Spec Sheet view.';
COMMENT ON COLUMN attribute_zone_xref.zone_locn_code IS E'A location on the railcar used to segment the Inbound Inspection process to use multiple forms. (Inbound Inpsection form is the only form using the Zone Location Code at this point in time.)';
CREATE INDEX attribute_zone_xref_fk1 ON attribute_zone_xref (zone_locn_code);
ALTER TABLE attribute_zone_xref ADD PRIMARY KEY (attribute_id,mr_locn_code);
ALTER TABLE attribute_zone_xref ALTER COLUMN attribute_id SET NOT NULL;
ALTER TABLE attribute_zone_xref ALTER COLUMN mr_locn_code SET NOT NULL;


CREATE TABLE cid_model_approval_number (
	model_approval_num_id integer NOT NULL,
	mfact_code varchar(4) NOT NULL,
	model_num varchar(50) NOT NULL,
	aar_approval_num varchar(11),
	component_code varchar(20) NOT NULL,
	mtrl_type varchar(40)
) ;
COMMENT ON COLUMN cid_model_approval_number.mfact_code IS E'A four-byte code that identifies the manufacturer of the pressure relief valve.';
COMMENT ON COLUMN cid_model_approval_number.model_approval_num_id IS E'surrogate key';
COMMENT ON COLUMN cid_model_approval_number.model_num IS E'The manufacturer''s model number for the pressure relief valve.';
ALTER TABLE cid_model_approval_number ADD UNIQUE (component_code,mfact_code,model_num);
ALTER TABLE cid_model_approval_number ADD PRIMARY KEY (model_approval_num_id);
ALTER TABLE cid_model_approval_number ALTER COLUMN model_approval_num_id SET NOT NULL;
ALTER TABLE cid_model_approval_number ALTER COLUMN mfact_code SET NOT NULL;
ALTER TABLE cid_model_approval_number ALTER COLUMN model_num SET NOT NULL;
ALTER TABLE cid_model_approval_number ALTER COLUMN component_code SET NOT NULL;


CREATE TABLE cid_shop_component_sequence (
	shop_component_sequence_id integer NOT NULL,
	shop_code varchar(4) NOT NULL,
	component_code varchar(15) NOT NULL,
	component_id_prefix smallint NOT NULL,
	component_id_last_sequence_num integer NOT NULL
) ;
COMMENT ON COLUMN cid_shop_component_sequence.shop_component_sequence_id IS E'surrogate id';
ALTER TABLE cid_shop_component_sequence ADD UNIQUE (shop_code,component_code);
ALTER TABLE cid_shop_component_sequence ADD PRIMARY KEY (shop_component_sequence_id);
ALTER TABLE cid_shop_component_sequence ALTER COLUMN shop_component_sequence_id SET NOT NULL;
ALTER TABLE cid_shop_component_sequence ALTER COLUMN shop_code SET NOT NULL;
ALTER TABLE cid_shop_component_sequence ALTER COLUMN component_code SET NOT NULL;
ALTER TABLE cid_shop_component_sequence ALTER COLUMN component_id_prefix SET NOT NULL;
ALTER TABLE cid_shop_component_sequence ALTER COLUMN component_id_last_sequence_num SET NOT NULL;


CREATE TABLE cid_umler_cross_reference (
	model_approval_num_id integer NOT NULL,
	mfact_model_no_id integer NOT NULL
) ;
COMMENT ON COLUMN cid_umler_cross_reference.model_approval_num_id IS E'surrogate key';
CREATE INDEX cid_umler_cross_reference_fk01 ON cid_umler_cross_reference (model_approval_num_id);
CREATE INDEX cid_umler_cross_reference_fk02 ON cid_umler_cross_reference (mfact_model_no_id);
ALTER TABLE cid_umler_cross_reference ADD PRIMARY KEY (mfact_model_no_id,model_approval_num_id);


CREATE TABLE cid_umler_oring_crossreference (
	seal_mtrl_id integer NOT NULL,
	umler_registration_value_id bigint
) ;
COMMENT ON COLUMN cid_umler_oring_crossreference.seal_mtrl_id IS E'surrogate key';
COMMENT ON COLUMN cid_umler_oring_crossreference.umler_registration_value_id IS E'surrogate key';
CREATE INDEX cid_umler_oring_xref_fk02 ON cid_umler_oring_crossreference (umler_registration_value_id);
ALTER TABLE cid_umler_oring_crossreference ADD PRIMARY KEY (seal_mtrl_id);
ALTER TABLE cid_umler_oring_crossreference ALTER COLUMN seal_mtrl_id SET NOT NULL;


CREATE TABLE cid_umler_registration_value (
	umler_registration_value_id bigint NOT NULL,
	component_code varchar(20) NOT NULL,
	aar_element_id varchar(4) NOT NULL,
	element_value varchar(50) NOT NULL,
	element_value_label varchar(100) NOT NULL,
	active_flag char(1) NOT NULL,
	gatx_value varchar(70),
	gatx_value_id integer
) ;
COMMENT ON COLUMN cid_umler_registration_value.umler_registration_value_id IS E'surrogate key';
ALTER TABLE cid_umler_registration_value ADD PRIMARY KEY (umler_registration_value_id);
ALTER TABLE cid_umler_registration_value ADD UNIQUE (component_code,aar_element_id,element_value);
ALTER TABLE cid_umler_registration_value ALTER COLUMN umler_registration_value_id SET NOT NULL;
ALTER TABLE cid_umler_registration_value ALTER COLUMN component_code SET NOT NULL;
ALTER TABLE cid_umler_registration_value ALTER COLUMN aar_element_id SET NOT NULL;
ALTER TABLE cid_umler_registration_value ALTER COLUMN element_value SET NOT NULL;
ALTER TABLE cid_umler_registration_value ALTER COLUMN element_value_label SET NOT NULL;
ALTER TABLE cid_umler_registration_value ALTER COLUMN active_flag SET NOT NULL;


CREATE TABLE cid_umler_test_result (
	umler_test_result_id bigint NOT NULL,
	aar_element_id varchar(4),
	component_code varchar(20) NOT NULL,
	element_name varchar(30) NOT NULL,
	existing_value_changed_flag varchar(1) NOT NULL,
	component_id_num varchar(14) NOT NULL,
	element_value varchar(100)
) ;
ALTER TABLE cid_umler_test_result ADD PRIMARY KEY (umler_test_result_id);
ALTER TABLE cid_umler_test_result ALTER COLUMN umler_test_result_id SET NOT NULL;
ALTER TABLE cid_umler_test_result ALTER COLUMN component_code SET NOT NULL;
ALTER TABLE cid_umler_test_result ALTER COLUMN element_name SET NOT NULL;
ALTER TABLE cid_umler_test_result ALTER COLUMN existing_value_changed_flag SET NOT NULL;
ALTER TABLE cid_umler_test_result ALTER COLUMN component_id_num SET NOT NULL;


CREATE TABLE coating_application_spec (
	coating_application_spec_id integer NOT NULL,
	class varchar(18),
	char_technical_name varchar(30),
	coating_application_car_type varchar(8),
	dropdown_source_id integer NOT NULL
) ;
COMMENT ON COLUMN coating_application_spec.coating_application_car_type IS E'This value will be Tank, Freight, Box.';
ALTER TABLE coating_application_spec ADD PRIMARY KEY (coating_application_spec_id);
ALTER TABLE coating_application_spec ALTER COLUMN coating_application_spec_id SET NOT NULL;
ALTER TABLE coating_application_spec ALTER COLUMN dropdown_source_id SET NOT NULL;


CREATE TABLE compartment_form_navigation (
	num_of_compartments smallint NOT NULL,
	compartment_order smallint NOT NULL,
	car_type varchar(10) NOT NULL,
	compartment varchar(6) NOT NULL
) ;
COMMENT ON TABLE compartment_form_navigation IS E'This table was created for the Coating Application form.';
ALTER TABLE compartment_form_navigation ADD PRIMARY KEY (num_of_compartments,compartment_order,car_type);
ALTER TABLE compartment_form_navigation ALTER COLUMN num_of_compartments SET NOT NULL;
ALTER TABLE compartment_form_navigation ALTER COLUMN compartment_order SET NOT NULL;
ALTER TABLE compartment_form_navigation ALTER COLUMN car_type SET NOT NULL;
ALTER TABLE compartment_form_navigation ALTER COLUMN compartment SET NOT NULL;


CREATE TABLE connection_type (
	connection_type_id integer NOT NULL,
	mfact_model_no_id integer NOT NULL,
	connection_type_name varchar(30) NOT NULL
) ;
CREATE INDEX connection_type_if1 ON connection_type (mfact_model_no_id);
ALTER TABLE connection_type ADD PRIMARY KEY (connection_type_id);
ALTER TABLE connection_type ALTER COLUMN connection_type_id SET NOT NULL;
ALTER TABLE connection_type ALTER COLUMN mfact_model_no_id SET NOT NULL;
ALTER TABLE connection_type ALTER COLUMN connection_type_name SET NOT NULL;


CREATE TABLE corrosion_type (
	corrosion_type_id integer NOT NULL,
	corrosion_type_name varchar(20) NOT NULL
) ;
ALTER TABLE corrosion_type ADD PRIMARY KEY (corrosion_type_id);
ALTER TABLE corrosion_type ALTER COLUMN corrosion_type_id SET NOT NULL;
ALTER TABLE corrosion_type ALTER COLUMN corrosion_type_name SET NOT NULL;


CREATE TABLE dci (
	dci_id integer NOT NULL,
	dci_form_id integer NOT NULL,
	dci_question_id integer NOT NULL,
	sequence_num decimal(5,2) NOT NULL,
	zone_locn_code varchar(2),
	dci_section_id integer,
	dci_group_id integer
) ;
COMMENT ON TABLE dci IS E'DCI contains the questions and the format of the response(s) required for a question on a form.';
COMMENT ON COLUMN dci.dci_form_id IS E'A unique key for the DCI Form table.';
COMMENT ON COLUMN dci.dci_group_id IS E'A unique, surrogate key for the DCI Group table.';
COMMENT ON COLUMN dci.dci_id IS E'A unique key for the DCI table.';
COMMENT ON COLUMN dci.dci_question_id IS E'A unique key for the DCI question table.';
COMMENT ON COLUMN dci.dci_section_id IS E'A unique, surrogate key for the DCI Section table.';
COMMENT ON COLUMN dci.sequence_num IS E'The sequence number determins the order of the DCIs on the form.';
CREATE INDEX dci_fk3 ON dci (dci_form_id);
CREATE INDEX dci_fk4 ON dci (dci_question_id);
CREATE INDEX dci_fk5 ON dci (zone_locn_code);
CREATE INDEX dci_fk6 ON dci (dci_section_id);
CREATE INDEX dci_fk7 ON dci (dci_group_id);
ALTER TABLE dci ADD PRIMARY KEY (dci_id);
ALTER TABLE dci ALTER COLUMN dci_id SET NOT NULL;
ALTER TABLE dci ALTER COLUMN dci_form_id SET NOT NULL;
ALTER TABLE dci ALTER COLUMN dci_question_id SET NOT NULL;
ALTER TABLE dci ALTER COLUMN sequence_num SET NOT NULL;


CREATE TABLE dci_form (
	dci_form_id integer NOT NULL,
	form_name_part_one varchar(40) NOT NULL,
	form_name_part_two varchar(40),
	form_template_id integer NOT NULL,
	form_num varchar(30) NOT NULL,
	sequence_num smallint NOT NULL,
	form_short_name varchar(40) NOT NULL,
	validate_flag varchar(1) NOT NULL DEFAULT 'N',
	auto_generated_flag varchar(1) NOT NULL DEFAULT 'N',
	cust_rpt_dstn_folder_name varchar(200),
	cust_rpt_flag varchar(1) DEFAULT 'N',
	export_in_prgrs_analytics_flag varchar(1),
	archiveable_flag varchar(1)
) ;
COMMENT ON TABLE dci_form IS E'DCI Form stores the Name, Section, and Form Template for a Form.
';
COMMENT ON COLUMN dci_form.archiveable_flag IS E'A ''Y'' indicates the user has the ability to archive this form.';
COMMENT ON COLUMN dci_form.auto_generated_flag IS E'This indicates forms which are auto-generated by SFE only after the forms have been validated (i.e. Validate Forms flag is set to ?Y?)';
COMMENT ON COLUMN dci_form.cust_rpt_dstn_folder_name IS E'Contains the destination folder name in the ECM binder structure.';
COMMENT ON COLUMN dci_form.cust_rpt_flag IS E'Indicates whether a DCI form contains a Customer Report print page.';
COMMENT ON COLUMN dci_form.dci_form_id IS E'A unique key for the DCI Form table.';
COMMENT ON COLUMN dci_form.export_in_prgrs_analytics_flag IS E'This flag indicates which forms should be extracted when they are in-progress.';
COMMENT ON COLUMN dci_form.form_name_part_one IS E'The first part of the Name that will appear at the top of the Form.';
COMMENT ON COLUMN dci_form.form_name_part_two IS E'The second part of the Name that will appear at the top of the Form.';
COMMENT ON COLUMN dci_form.form_short_name IS E'The short name ofr the form that will be used to identify the
form on the Print List portet and the Navigation List portlet.';
COMMENT ON COLUMN dci_form.form_template_id IS E'An sequential id used to uniquely identify the template.';
COMMENT ON COLUMN dci_form.validate_flag IS E'This indicates which forms are to be extracted and sent in the request to validate whether users? form content responses are consistent with one another.';
CREATE INDEX dci_form_fk3 ON dci_form (form_template_id);
ALTER TABLE dci_form ADD PRIMARY KEY (dci_form_id);
ALTER TABLE dci_form ALTER COLUMN dci_form_id SET NOT NULL;
ALTER TABLE dci_form ALTER COLUMN form_name_part_one SET NOT NULL;
ALTER TABLE dci_form ALTER COLUMN form_template_id SET NOT NULL;
ALTER TABLE dci_form ALTER COLUMN form_num SET NOT NULL;
ALTER TABLE dci_form ALTER COLUMN sequence_num SET NOT NULL;
ALTER TABLE dci_form ALTER COLUMN form_short_name SET NOT NULL;
ALTER TABLE dci_form ALTER COLUMN validate_flag SET NOT NULL;
ALTER TABLE dci_form ALTER COLUMN auto_generated_flag SET NOT NULL;


CREATE TABLE dci_group (
	dci_group_id integer NOT NULL,
	dci_group_name varchar(50),
	dci_group_code varchar(5)
) ;
COMMENT ON TABLE dci_group IS E'The DCI Group is a grouping of DCIs on a form. For example, on the form with fitting categories, the DCI Group is the fitting categories and includes the DCI for the Equipped/Not Equipped response and the DCIs for the VT, BT and CT for that fitting category.';
COMMENT ON COLUMN dci_group.dci_group_code IS E'The code assigned to the DCI Group.';
COMMENT ON COLUMN dci_group.dci_group_id IS E'A unique, surrogate key for the DCI Group table.';
COMMENT ON COLUMN dci_group.dci_group_name IS E'The name of the DCI Group.';
ALTER TABLE dci_group ADD PRIMARY KEY (dci_group_id);
ALTER TABLE dci_group ALTER COLUMN dci_group_id SET NOT NULL;


CREATE TABLE dci_question (
	dci_question_id integer NOT NULL,
	dci_question varchar(150) NOT NULL,
	insp_method varchar(40),
	cutout_zone varchar(20),
	tcid_component_id integer,
	dci_question_type_id smallint,
	part_type_code varchar(5),
	secondary_insp_method varchar(40),
	dci_question_detail varchar(500),
	tcid_chng_catg varchar(20),
	tcid_defect_indicator_id integer
) ;
COMMENT ON COLUMN dci_question.dci_question_detail IS E'Added for story # 8079';
CREATE INDEX dci_question_fk1 ON dci_question (tcid_component_id);
CREATE INDEX dci_question_fk2 ON dci_question (dci_question_type_id);
CREATE INDEX dci_question_fk3 ON dci_question (tcid_defect_indicator_id);
ALTER TABLE dci_question ADD PRIMARY KEY (dci_question_id);
ALTER TABLE dci_question ALTER COLUMN dci_question_id SET NOT NULL;
ALTER TABLE dci_question ALTER COLUMN dci_question SET NOT NULL;


CREATE TABLE dci_question_attribute (
	dci_question_id integer NOT NULL,
	attribute_id bigint NOT NULL
) ;
COMMENT ON COLUMN dci_question_attribute.attribute_id IS E'The Portal Category used in the Shop Car Spec Sheet view.';
CREATE INDEX dci_question_attribute_fk1 ON dci_question_attribute (dci_question_id);
ALTER TABLE dci_question_attribute ADD PRIMARY KEY (dci_question_id,attribute_id);
ALTER TABLE dci_question_attribute ALTER COLUMN dci_question_id SET NOT NULL;
ALTER TABLE dci_question_attribute ALTER COLUMN attribute_id SET NOT NULL;


CREATE TABLE dci_question_dropdown_source (
	dci_question_id integer NOT NULL,
	dropdown_source_id integer NOT NULL
) ;
CREATE INDEX dci_question_ddown_source_fk1 ON dci_question_dropdown_source (dci_question_id);
ALTER TABLE dci_question_dropdown_source ADD PRIMARY KEY (dci_question_id,dropdown_source_id);
ALTER TABLE dci_question_dropdown_source ALTER COLUMN dci_question_id SET NOT NULL;
ALTER TABLE dci_question_dropdown_source ALTER COLUMN dropdown_source_id SET NOT NULL;


CREATE TABLE dci_question_type (
	dci_question_type_id smallint NOT NULL,
	dci_question_type_desc varchar(20)
) ;
ALTER TABLE dci_question_type ADD PRIMARY KEY (dci_question_type_id);
ALTER TABLE dci_question_type ALTER COLUMN dci_question_type_id SET NOT NULL;


CREATE TABLE dci_response_pattern_group (
	dci_response_pattern_group_id integer NOT NULL,
	dci_question_id integer NOT NULL,
	response_option_id integer NOT NULL,
	pattern_group_id integer NOT NULL,
	display_sequence smallint NOT NULL,
	dropdown_source_id integer NOT NULL,
	default_print_pattern_flag varchar(1) NOT NULL,
	secondary_pattern_group_id integer
) ;
COMMENT ON COLUMN dci_response_pattern_group.default_print_pattern_flag IS E'Thi flag indicates that this pattern/response will be the default when printing a blank form.';
COMMENT ON COLUMN dci_response_pattern_group.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
COMMENT ON COLUMN dci_response_pattern_group.secondary_pattern_group_id IS E'A surrogate key to identify a Pattern Group. Secondary Pattern Group Identifier is for another set of data collection items associated to the Pattern Group Identifier. This is currently only used to collect TCID data values in the Benchmark Thickness and Defect Thickness forms.';
CREATE INDEX dci_response_pattern_group_fk4 ON dci_response_pattern_group (pattern_group_id);
CREATE INDEX dci_response_pattern_group_fk6 ON dci_response_pattern_group (response_option_id);
CREATE INDEX dci_response_pattern_group_fk7 ON dci_response_pattern_group (dci_question_id);
CREATE INDEX dci_response_pattern_group_fk8 ON dci_response_pattern_group (secondary_pattern_group_id);
ALTER TABLE dci_response_pattern_group ADD PRIMARY KEY (dci_response_pattern_group_id);
ALTER TABLE dci_response_pattern_group ALTER COLUMN dci_response_pattern_group_id SET NOT NULL;
ALTER TABLE dci_response_pattern_group ALTER COLUMN dci_question_id SET NOT NULL;
ALTER TABLE dci_response_pattern_group ALTER COLUMN response_option_id SET NOT NULL;
ALTER TABLE dci_response_pattern_group ALTER COLUMN pattern_group_id SET NOT NULL;
ALTER TABLE dci_response_pattern_group ALTER COLUMN display_sequence SET NOT NULL;
ALTER TABLE dci_response_pattern_group ALTER COLUMN dropdown_source_id SET NOT NULL;
ALTER TABLE dci_response_pattern_group ALTER COLUMN default_print_pattern_flag SET NOT NULL;


CREATE TABLE dci_section (
	dci_section_id integer NOT NULL,
	dci_section_name varchar(50),
	dci_section_code varchar(5)
) ;
COMMENT ON TABLE dci_section IS E'The DCI Section groups DCI based on the section of a form. For example, in the form with the fitting categories, there is a section for the VT, a section for the BT, etc.';
COMMENT ON COLUMN dci_section.dci_section_code IS E'The code assigned to the DCI Section.';
COMMENT ON COLUMN dci_section.dci_section_id IS E'A unique, surrogate key for the DCI Section table.';
COMMENT ON COLUMN dci_section.dci_section_name IS E'The name of the DCI Section.';
ALTER TABLE dci_section ADD PRIMARY KEY (dci_section_id);
ALTER TABLE dci_section ALTER COLUMN dci_section_id SET NOT NULL;


CREATE TABLE defect (
	defect_code_id integer NOT NULL,
	defect_code varchar(2) NOT NULL,
	defect_name varchar(120) NOT NULL,
	dropdown_source_id integer,
	insp_point_id integer
) ;
COMMENT ON COLUMN defect.defect_code_id IS E'Surrogate key';
COMMENT ON COLUMN defect.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
CREATE INDEX defect_fk01 ON defect (insp_point_id);
ALTER TABLE defect ADD PRIMARY KEY (defect_code_id);
ALTER TABLE defect ALTER COLUMN defect_code_id SET NOT NULL;
ALTER TABLE defect ALTER COLUMN defect_code SET NOT NULL;
ALTER TABLE defect ALTER COLUMN defect_name SET NOT NULL;


CREATE TABLE diagram (
	diagram_id smallint NOT NULL,
	num_of_compartments_qty smallint NOT NULL,
	dropdown_source_id integer NOT NULL,
	file_name varchar(100) NOT NULL,
	diagram_num smallint NOT NULL
) ;
COMMENT ON COLUMN diagram.diagram_id IS E'A surrogate key to uniquely identify each row.';
COMMENT ON COLUMN diagram.diagram_num IS E'Identifier used by the form to place diagrams contextually in the form.';
COMMENT ON COLUMN diagram.dropdown_source_id IS E'A number to identify  associated data to be provided to the form for a form question.';
COMMENT ON COLUMN diagram.file_name IS E'File name of the image';
COMMENT ON COLUMN diagram.num_of_compartments_qty IS E'The number of compartments for a given car.';
ALTER TABLE diagram ADD PRIMARY KEY (diagram_id);
ALTER TABLE diagram ALTER COLUMN diagram_id SET NOT NULL;
ALTER TABLE diagram ALTER COLUMN num_of_compartments_qty SET NOT NULL;
ALTER TABLE diagram ALTER COLUMN dropdown_source_id SET NOT NULL;
ALTER TABLE diagram ALTER COLUMN file_name SET NOT NULL;
ALTER TABLE diagram ALTER COLUMN diagram_num SET NOT NULL;


CREATE TABLE disc_test_pressure (
	disc_pressure decimal(4,1) NOT NULL,
	disc_test_pressure decimal(4,1)
) ;
COMMENT ON COLUMN disc_test_pressure.disc_pressure IS E'The pressure of the disc installed in a Non Reclosing PRD vent.';
COMMENT ON COLUMN disc_test_pressure.disc_test_pressure IS E'The pressure to bench test a Non Reclosing PRD vent.';
ALTER TABLE disc_test_pressure ADD PRIMARY KEY (disc_pressure);
ALTER TABLE disc_test_pressure ALTER COLUMN disc_pressure SET NOT NULL;


CREATE TABLE dropdown_list (
	dropdown_list_id numeric(38) NOT NULL,
	dropdown_list_name varchar(20) NOT NULL
) ;
ALTER TABLE dropdown_list ADD PRIMARY KEY (dropdown_list_id);
ALTER TABLE dropdown_list ALTER COLUMN dropdown_list_id SET NOT NULL;
ALTER TABLE dropdown_list ALTER COLUMN dropdown_list_name SET NOT NULL;


CREATE TABLE dropdown_list_content (
	dropdown_list_content_id integer NOT NULL,
	dropdown_list_id integer NOT NULL,
	dropdown_list_value varchar(50) NOT NULL,
	dropdown_source_id integer
) ;
COMMENT ON COLUMN dropdown_list_content.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
CREATE INDEX dropdown_list_content_fk2 ON dropdown_list_content (dropdown_list_id);
ALTER TABLE dropdown_list_content ADD PRIMARY KEY (dropdown_list_content_id);
ALTER TABLE dropdown_list_content ALTER COLUMN dropdown_list_content_id SET NOT NULL;
ALTER TABLE dropdown_list_content ALTER COLUMN dropdown_list_id SET NOT NULL;
ALTER TABLE dropdown_list_content ALTER COLUMN dropdown_list_value SET NOT NULL;


CREATE TABLE dropdown_list_content_level_2 (
	dropdown_list_content_lvl_2_id integer NOT NULL,
	dropdown_list_content_id integer,
	dropdown_list_value varchar(50) NOT NULL
) ;
CREATE INDEX dropdown_list_cont_lvl_2_fk1 ON dropdown_list_content_level_2 (dropdown_list_content_id);
ALTER TABLE dropdown_list_content_level_2 ADD PRIMARY KEY (dropdown_list_content_lvl_2_id);
ALTER TABLE dropdown_list_content_level_2 ALTER COLUMN dropdown_list_content_lvl_2_id SET NOT NULL;
ALTER TABLE dropdown_list_content_level_2 ALTER COLUMN dropdown_list_value SET NOT NULL;


CREATE TABLE figure (
	figure_id smallint NOT NULL,
	underframe varchar(20),
	figure varchar(20) NOT NULL,
	item varchar(20),
	reference_doc_id integer NOT NULL
) ;
CREATE INDEX figure_fk01 ON figure (reference_doc_id);
ALTER TABLE figure ADD PRIMARY KEY (figure_id);
ALTER TABLE figure ALTER COLUMN figure_id SET NOT NULL;
ALTER TABLE figure ALTER COLUMN figure SET NOT NULL;
ALTER TABLE figure ALTER COLUMN reference_doc_id SET NOT NULL;


CREATE TABLE form_template (
	form_template_id integer NOT NULL,
	created_date timestamp(0),
	created_by_login_id varchar(30),
	last_modified_date timestamp(0),
	last_modified_login_id varchar(30),
	template_desc varchar(100) NOT NULL,
	form_template text
) ;
COMMENT ON COLUMN form_template.created_by_login_id IS E'The login id of the person who created the template.';
COMMENT ON COLUMN form_template.created_date IS E'Date on which the template was created.';
COMMENT ON COLUMN form_template.form_template IS E'The template used as a base for populating the form.';
COMMENT ON COLUMN form_template.form_template_id IS E'An sequential id used to uniquely identify the template.';
COMMENT ON COLUMN form_template.last_modified_date IS E'Date on which the template was modified.';
COMMENT ON COLUMN form_template.last_modified_login_id IS E'The login id of the person who last modified the template.';
COMMENT ON COLUMN form_template.template_desc IS E'The description for the template for identification in the user interface.';
ALTER TABLE form_template ADD PRIMARY KEY (form_template_id);
ALTER TABLE form_template ALTER COLUMN form_template_id SET NOT NULL;
ALTER TABLE form_template ALTER COLUMN template_desc SET NOT NULL;


CREATE TABLE high_test_pressure (
	tank_test_pressure smallint NOT NULL,
	min_high_test_pressure smallint,
	max_high_test_pressure smallint
) ;
COMMENT ON COLUMN high_test_pressure.max_high_test_pressure IS E'The maximum high test pressure allowed for bench testing a Reclosing PRD valve.';
COMMENT ON COLUMN high_test_pressure.min_high_test_pressure IS E'The minimum high test pressure allowed for bench testing a Reclosing PRD valve.';
COMMENT ON COLUMN high_test_pressure.tank_test_pressure IS E'The Tank Test Pressure that is derived from the Stencil Class Code for the railcar.';
ALTER TABLE high_test_pressure ADD PRIMARY KEY (tank_test_pressure);
ALTER TABLE high_test_pressure ALTER COLUMN tank_test_pressure SET NOT NULL;


CREATE TABLE inspection_point (
	insp_point_id integer NOT NULL,
	insp_point_name varchar(170) NOT NULL,
	dropdown_source_id integer NOT NULL
) ;
ALTER TABLE inspection_point ADD PRIMARY KEY (insp_point_id);
ALTER TABLE inspection_point ALTER COLUMN insp_point_id SET NOT NULL;
ALTER TABLE inspection_point ALTER COLUMN insp_point_name SET NOT NULL;
ALTER TABLE inspection_point ALTER COLUMN dropdown_source_id SET NOT NULL;


CREATE TABLE inspection_point_defect_rank (
	defect_code_id integer NOT NULL,
	dci_question_id integer NOT NULL,
	insp_point_id integer NOT NULL,
	insp_point_defect_rank smallint
) ;
COMMENT ON TABLE inspection_point_defect_rank IS E'The Inspection Point Defect Rank table is used in the Service Equipment Analytics datamart to determine the primary Defect for an Inspection Point for a failure when more than one Defect was recorded for an Inspection Point on a Fitting.';
COMMENT ON COLUMN inspection_point_defect_rank.defect_code_id IS E'Surrogate key';
CREATE INDEX insp_point_defect_rank_fk1 ON inspection_point_defect_rank (defect_code_id);
CREATE INDEX insp_point_defect_rank_fk2 ON inspection_point_defect_rank (dci_question_id);
CREATE INDEX insp_point_defect_rank_fk3 ON inspection_point_defect_rank (insp_point_id);
ALTER TABLE inspection_point_defect_rank ADD PRIMARY KEY (defect_code_id,dci_question_id,insp_point_id);
ALTER TABLE inspection_point_defect_rank ALTER COLUMN defect_code_id SET NOT NULL;
ALTER TABLE inspection_point_defect_rank ALTER COLUMN dci_question_id SET NOT NULL;
ALTER TABLE inspection_point_defect_rank ALTER COLUMN insp_point_id SET NOT NULL;


CREATE TABLE inspection_point_rank (
	insp_point_id integer NOT NULL,
	dci_question_id integer NOT NULL,
	insp_point_rank smallint
) ;
COMMENT ON TABLE inspection_point_rank IS E'The Inspection Point Rank table is used in the Service Equipment Analytics datamart to determine the primary Inspection Point for a failure when more than one Inspection Point was recorded for a Fitting.';
CREATE INDEX inspection_point_rank_fk1 ON inspection_point_rank (insp_point_id);
CREATE INDEX inspection_point_rank_fk2 ON inspection_point_rank (dci_question_id);
ALTER TABLE inspection_point_rank ADD PRIMARY KEY (insp_point_id,dci_question_id);
ALTER TABLE inspection_point_rank ALTER COLUMN insp_point_id SET NOT NULL;
ALTER TABLE inspection_point_rank ALTER COLUMN dci_question_id SET NOT NULL;


CREATE TABLE inspection_point_zone_location (
	insp_point_zone_locn_id integer NOT NULL,
	insp_point_id integer NOT NULL,
	zone_locn_code varchar(2) NOT NULL
) ;
CREATE INDEX inspection_point_zone_locn_fk1 ON inspection_point_zone_location (insp_point_id);
CREATE INDEX inspection_point_zone_locn_fk2 ON inspection_point_zone_location (zone_locn_code);
ALTER TABLE inspection_point_zone_location ADD PRIMARY KEY (insp_point_zone_locn_id);
ALTER TABLE inspection_point_zone_location ALTER COLUMN insp_point_zone_locn_id SET NOT NULL;
ALTER TABLE inspection_point_zone_location ALTER COLUMN insp_point_id SET NOT NULL;
ALTER TABLE inspection_point_zone_location ALTER COLUMN zone_locn_code SET NOT NULL;


CREATE TABLE interior_exterior (
	interior_exterior_name varchar(20) NOT NULL,
	interior_exterior_id smallint NOT NULL
) ;
ALTER TABLE interior_exterior ADD PRIMARY KEY (interior_exterior_id);
ALTER TABLE interior_exterior ALTER COLUMN interior_exterior_name SET NOT NULL;
ALTER TABLE interior_exterior ALTER COLUMN interior_exterior_id SET NOT NULL;


CREATE TABLE job_code (
	job_code_id integer NOT NULL,
	job_code varchar(40) NOT NULL,
	job_code_name varchar(60) NOT NULL,
	insp_point_id integer,
	dropdown_source_id integer
) ;
COMMENT ON COLUMN job_code.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
CREATE INDEX job_code_fk01 ON job_code (insp_point_id);
ALTER TABLE job_code ADD PRIMARY KEY (job_code_id);
ALTER TABLE job_code ALTER COLUMN job_code_id SET NOT NULL;
ALTER TABLE job_code ALTER COLUMN job_code SET NOT NULL;
ALTER TABLE job_code ALTER COLUMN job_code_name SET NOT NULL;


CREATE TABLE location (
	locn_id integer NOT NULL,
	locn_name varchar(40) NOT NULL,
	num_of_rings bigint NOT NULL,
	dropdown_source_id integer NOT NULL,
	location_group_id smallint NOT NULL
) ;
COMMENT ON COLUMN location.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
COMMENT ON COLUMN location.location_group_id IS E'A unique identifier for a Location Group.';
CREATE INDEX location_if1 ON location (location_group_id);
ALTER TABLE location ADD PRIMARY KEY (locn_id);
ALTER TABLE location ALTER COLUMN locn_id SET NOT NULL;
ALTER TABLE location ALTER COLUMN locn_name SET NOT NULL;
ALTER TABLE location ALTER COLUMN num_of_rings SET NOT NULL;
ALTER TABLE location ALTER COLUMN dropdown_source_id SET NOT NULL;
ALTER TABLE location ALTER COLUMN location_group_id SET NOT NULL;


CREATE TABLE location_corrosion_type (
	dropdown_source_id integer NOT NULL,
	locn_group_id smallint NOT NULL,
	corrosion_type_id integer NOT NULL
) ;
COMMENT ON COLUMN location_corrosion_type.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
COMMENT ON COLUMN location_corrosion_type.locn_group_id IS E'A unique identifier for a Location Group.';
CREATE INDEX location_corrosion_type_fk1 ON location_corrosion_type (locn_group_id);
CREATE INDEX location_corrosion_type_fk2 ON location_corrosion_type (corrosion_type_id);
ALTER TABLE location_corrosion_type ADD PRIMARY KEY (dropdown_source_id,locn_group_id,corrosion_type_id);
ALTER TABLE location_corrosion_type ALTER COLUMN dropdown_source_id SET NOT NULL;
ALTER TABLE location_corrosion_type ALTER COLUMN locn_group_id SET NOT NULL;
ALTER TABLE location_corrosion_type ALTER COLUMN corrosion_type_id SET NOT NULL;


CREATE TABLE location_group (
	locn_group_id smallint NOT NULL,
	locn_group_name varchar(30) NOT NULL
) ;
COMMENT ON COLUMN location_group.locn_group_id IS E'A unique identifier for a Location Group.';
COMMENT ON COLUMN location_group.locn_group_name IS E'The name of the Locaiton Group. Examples are Top Shell, Bottom Shell, etc.)';
ALTER TABLE location_group ADD PRIMARY KEY (locn_group_id);
ALTER TABLE location_group ALTER COLUMN locn_group_id SET NOT NULL;
ALTER TABLE location_group ALTER COLUMN locn_group_name SET NOT NULL;


CREATE TABLE location_interior_exterior (
	dropdown_source_id integer NOT NULL,
	locn_group_id smallint NOT NULL,
	interior_exterior_id smallint NOT NULL
) ;
COMMENT ON COLUMN location_interior_exterior.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
COMMENT ON COLUMN location_interior_exterior.locn_group_id IS E'A unique identifier for a Location Group.';
CREATE INDEX location_interior_exterior_fk1 ON location_interior_exterior (locn_group_id);
CREATE INDEX location_interior_exterior_fk2 ON location_interior_exterior (interior_exterior_id);
ALTER TABLE location_interior_exterior ADD PRIMARY KEY (dropdown_source_id,locn_group_id,interior_exterior_id);
ALTER TABLE location_interior_exterior ALTER COLUMN dropdown_source_id SET NOT NULL;
ALTER TABLE location_interior_exterior ALTER COLUMN locn_group_id SET NOT NULL;
ALTER TABLE location_interior_exterior ALTER COLUMN interior_exterior_id SET NOT NULL;


CREATE TABLE manufacturer (
	manufacturer_id integer NOT NULL,
	mfact_name varchar(50) NOT NULL,
	dropdown_source_id integer NOT NULL
) ;
COMMENT ON COLUMN manufacturer.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
ALTER TABLE manufacturer ADD PRIMARY KEY (manufacturer_id);
ALTER TABLE manufacturer ALTER COLUMN manufacturer_id SET NOT NULL;
ALTER TABLE manufacturer ALTER COLUMN mfact_name SET NOT NULL;
ALTER TABLE manufacturer ALTER COLUMN dropdown_source_id SET NOT NULL;


CREATE TABLE manufacturer_model_no (
	mfact_model_no_id integer NOT NULL,
	mfact_id integer NOT NULL,
	model_num varchar(30) NOT NULL,
	nominal_std_pressure decimal(5,2)
) ;
CREATE INDEX manufacturer_model_no_if1 ON manufacturer_model_no (mfact_id);
ALTER TABLE manufacturer_model_no ADD PRIMARY KEY (mfact_model_no_id);
ALTER TABLE manufacturer_model_no ALTER COLUMN mfact_model_no_id SET NOT NULL;
ALTER TABLE manufacturer_model_no ALTER COLUMN mfact_id SET NOT NULL;
ALTER TABLE manufacturer_model_no ALTER COLUMN model_num SET NOT NULL;


CREATE TABLE material (
	mtrl_id integer NOT NULL,
	mfact_model_no_id integer NOT NULL,
	mtrl_name varchar(30) NOT NULL
) ;
CREATE INDEX material_if1 ON material (mfact_model_no_id);
ALTER TABLE material ADD PRIMARY KEY (mtrl_id);
ALTER TABLE material ALTER COLUMN mtrl_id SET NOT NULL;
ALTER TABLE material ALTER COLUMN mfact_model_no_id SET NOT NULL;
ALTER TABLE material ALTER COLUMN mtrl_name SET NOT NULL;


CREATE TABLE model_number_suffix (
	model_suffix_id integer NOT NULL,
	mfact_model_no_id integer,
	model_num_suffix varchar(25)
) ;
CREATE INDEX model_number_suffix_if1 ON model_number_suffix (mfact_model_no_id);
ALTER TABLE model_number_suffix ADD PRIMARY KEY (model_suffix_id);
ALTER TABLE model_number_suffix ALTER COLUMN model_suffix_id SET NOT NULL;


CREATE TABLE model_size (
	model_size_id integer NOT NULL,
	mfact_model_no_id integer NOT NULL,
	size_name varchar(20) NOT NULL,
	shape_id integer
) ;
CREATE INDEX model_size_if1 ON model_size (mfact_model_no_id);
CREATE INDEX model_size_if2 ON model_size (shape_id);
ALTER TABLE model_size ADD PRIMARY KEY (model_size_id);
ALTER TABLE model_size ALTER COLUMN model_size_id SET NOT NULL;
ALTER TABLE model_size ALTER COLUMN mfact_model_no_id SET NOT NULL;
ALTER TABLE model_size ALTER COLUMN size_name SET NOT NULL;


CREATE TABLE not_tested_reason (
	not_tested_reason_id integer NOT NULL,
	not_tested_reason_code varchar(5),
	not_tested_reason_desc varchar(100),
	dropdown_source_id integer NOT NULL
) ;
COMMENT ON COLUMN not_tested_reason.dropdown_source_id IS E'An identifier to group Not Tested Reasons for use in a dropdown list on a form.';
COMMENT ON COLUMN not_tested_reason.not_tested_reason_code IS E'A code to indicate the reason why a part was not tested. ';
COMMENT ON COLUMN not_tested_reason.not_tested_reason_desc IS E'A corresponding description for the reason why a part was not tested.';
COMMENT ON COLUMN not_tested_reason.not_tested_reason_id IS E'surrogate key';
ALTER TABLE not_tested_reason ADD PRIMARY KEY (not_tested_reason_id);
ALTER TABLE not_tested_reason ALTER COLUMN not_tested_reason_id SET NOT NULL;
ALTER TABLE not_tested_reason ALTER COLUMN dropdown_source_id SET NOT NULL;


CREATE TABLE nozzle_condemnable_limit (
	tank_shell_mtrl varchar(30) NOT NULL,
	tank_test_pressure_min smallint NOT NULL,
	tank_test_pressure_max smallint NOT NULL,
	locn_group_id smallint NOT NULL,
	condemnable_lmt decimal(6,3) NOT NULL
) ;
COMMENT ON COLUMN nozzle_condemnable_limit.locn_group_id IS E'A unique identifier for a Location Group.';
CREATE INDEX nozzle_condemnable_limit_fk1 ON nozzle_condemnable_limit (locn_group_id);
ALTER TABLE nozzle_condemnable_limit ADD PRIMARY KEY (tank_shell_mtrl,tank_test_pressure_min,tank_test_pressure_max,locn_group_id);
ALTER TABLE nozzle_condemnable_limit ALTER COLUMN tank_shell_mtrl SET NOT NULL;
ALTER TABLE nozzle_condemnable_limit ALTER COLUMN tank_test_pressure_min SET NOT NULL;
ALTER TABLE nozzle_condemnable_limit ALTER COLUMN tank_test_pressure_max SET NOT NULL;
ALTER TABLE nozzle_condemnable_limit ALTER COLUMN locn_group_id SET NOT NULL;
ALTER TABLE nozzle_condemnable_limit ALTER COLUMN condemnable_lmt SET NOT NULL;


CREATE TABLE part_location (
	part_locn_code varchar(15),
	car_type_code varchar(2),
	part_type_code varchar(20),
	part_locn_id integer NOT NULL
) ;
COMMENT ON COLUMN part_location.part_locn_code IS E'The code for the Seal Material.';
COMMENT ON COLUMN part_location.part_locn_id IS E'surrogate key';
ALTER TABLE part_location ADD PRIMARY KEY (part_locn_id);
ALTER TABLE part_location ALTER COLUMN part_locn_id SET NOT NULL;


CREATE TABLE pattern_group (
	pattern_group_id integer NOT NULL,
	pattern_group_code varchar(30) NOT NULL
) ;
ALTER TABLE pattern_group ADD PRIMARY KEY (pattern_group_id);
ALTER TABLE pattern_group ALTER COLUMN pattern_group_id SET NOT NULL;
ALTER TABLE pattern_group ALTER COLUMN pattern_group_code SET NOT NULL;


CREATE TABLE pressure_rating (
	pressure_rating_id integer NOT NULL,
	mfact_model_no_id integer,
	pressure_rating varchar(6)
) ;
CREATE INDEX pressure_rating_if1 ON pressure_rating (mfact_model_no_id);
ALTER TABLE pressure_rating ADD PRIMARY KEY (pressure_rating_id);
ALTER TABLE pressure_rating ALTER COLUMN pressure_rating_id SET NOT NULL;


CREATE TABLE qualification_decal_last_rpt (
	service_event_id bigint NOT NULL,
	sequence_num bigint NOT NULL,
	qualification_label_one varchar(20),
	qualification_label_two varchar(20),
	last_rpt_stn_code varchar(4),
	last_rpt_qualified_year varchar(4),
	last_rpt_due_year varchar(8)
) ;
COMMENT ON COLUMN qualification_decal_last_rpt.service_event_id IS E'A unique for the Service event, which is automatically generated by the database.';
CREATE INDEX qualifictn_decal_lst_rpt_fk1 ON qualification_decal_last_rpt (service_event_id);
ALTER TABLE qualification_decal_last_rpt ADD PRIMARY KEY (service_event_id,sequence_num);
ALTER TABLE qualification_decal_last_rpt ALTER COLUMN service_event_id SET NOT NULL;
ALTER TABLE qualification_decal_last_rpt ALTER COLUMN sequence_num SET NOT NULL;


CREATE TABLE qualification_decal_upon_compl (
	service_event_id bigint NOT NULL,
	sequence_num bigint NOT NULL,
	qualification_label_one varchar(20),
	qualification_label_two varchar(20),
	upon_completion_stn_code varchar(4),
	upon_completion_qualified_year varchar(4),
	upon_completion_due_year varchar(8),
	criteria_for_due_date varchar(500)
) ;
COMMENT ON COLUMN qualification_decal_upon_compl.service_event_id IS E'A unique for the Service event, which is automatically generated by the database.';
ALTER TABLE qualification_decal_upon_compl ADD PRIMARY KEY (service_event_id,sequence_num);
ALTER TABLE qualification_decal_upon_compl ALTER COLUMN service_event_id SET NOT NULL;
ALTER TABLE qualification_decal_upon_compl ALTER COLUMN sequence_num SET NOT NULL;


CREATE TABLE qualifier (
	qualifier_id integer NOT NULL,
	qualifier_code varchar(2) NOT NULL,
	qualifier_name varchar(80) NOT NULL,
	insp_point_id integer,
	dropdown_source_id integer
) ;
COMMENT ON COLUMN qualifier.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
CREATE INDEX qualifier_fk01 ON qualifier (insp_point_id);
ALTER TABLE qualifier ADD PRIMARY KEY (qualifier_id);
ALTER TABLE qualifier ALTER COLUMN qualifier_id SET NOT NULL;
ALTER TABLE qualifier ALTER COLUMN qualifier_code SET NOT NULL;
ALTER TABLE qualifier ALTER COLUMN qualifier_name SET NOT NULL;


CREATE TABLE qual_decal_uponcompletion_chng (
	service_event_id bigint NOT NULL,
	chng_cmnt varchar(500),
	last_modified_date timestamp(0),
	last_modified_login_id varchar(30)
) ;
COMMENT ON COLUMN qual_decal_uponcompletion_chng.last_modified_date IS E'The date on which the row in the Customer table was last modified.';
ALTER TABLE qual_decal_uponcompletion_chng ADD PRIMARY KEY (service_event_id);
ALTER TABLE qual_decal_uponcompletion_chng ALTER COLUMN service_event_id SET NOT NULL;


CREATE TABLE reference_document (
	reference_doc_id integer NOT NULL,
	reference_doc varchar(100) NOT NULL,
	program_type_code varchar(2),
	program_num bigint,
	program_rvsn_code varchar(1),
	dci_question_id integer NOT NULL,
	reference_doc_type varchar(10),
	dspl_in_shopping_instruc_flag varchar(1)
) ;
COMMENT ON COLUMN reference_document.dspl_in_shopping_instruc_flag IS E'If this flag is set to ''Y'', then an additional message will appear for this program in the shopping instrucions portlet.';
COMMENT ON COLUMN reference_document.reference_doc_type IS E'Added for story # 8167';
ALTER TABLE reference_document ADD PRIMARY KEY (reference_doc_id);
ALTER TABLE reference_document ALTER COLUMN reference_doc_id SET NOT NULL;
ALTER TABLE reference_document ALTER COLUMN reference_doc SET NOT NULL;
ALTER TABLE reference_document ALTER COLUMN dci_question_id SET NOT NULL;


CREATE TABLE required_dci (
	required_dci_id bigint NOT NULL,
	dci_id integer NOT NULL,
	required_form_id bigint NOT NULL
) ;
COMMENT ON TABLE required_dci IS E'Required DCI records all of the DCIs that are required for a particular form for a service event. This determination is done by ILOG.';
COMMENT ON COLUMN required_dci.dci_id IS E'The identifier for the DCI reference table.';
COMMENT ON COLUMN required_dci.required_dci_id IS E'A unique primary key for the required DCI for this form.
';
COMMENT ON COLUMN required_dci.required_form_id IS E'A unique key for the Required Form table, which is automatically generated by the database.';
CREATE INDEX required_dci_fk1 ON required_dci (required_form_id);
ALTER TABLE required_dci ADD PRIMARY KEY (required_dci_id);
ALTER TABLE required_dci ALTER COLUMN required_dci_id SET NOT NULL;
ALTER TABLE required_dci ALTER COLUMN dci_id SET NOT NULL;
ALTER TABLE required_dci ALTER COLUMN required_form_id SET NOT NULL;


CREATE TABLE required_dci_program (
	required_dci_program_id bigint NOT NULL,
	required_dci_id bigint NOT NULL,
	service_event_required_prog_id bigint NOT NULL
) ;
COMMENT ON TABLE required_dci_program IS E'Required DCI Program records the Maintenance Program that is associated with a DCI on a form for a service event.';
COMMENT ON COLUMN required_dci_program.required_dci_id IS E'A unique primary key for the required DCI for this form.
';
COMMENT ON COLUMN required_dci_program.required_dci_program_id IS E'The Required DCI Program Identifier is a unique identifier for a program associated with a DCI in a form for a service event.';
CREATE INDEX required_dci_program_fk1 ON required_dci_program (required_dci_id);
CREATE INDEX required_dci_program_fk2 ON required_dci_program (service_event_required_prog_id);
ALTER TABLE required_dci_program ADD PRIMARY KEY (required_dci_program_id);
ALTER TABLE required_dci_program ALTER COLUMN required_dci_program_id SET NOT NULL;
ALTER TABLE required_dci_program ALTER COLUMN required_dci_id SET NOT NULL;
ALTER TABLE required_dci_program ALTER COLUMN service_event_required_prog_id SET NOT NULL;


CREATE TABLE required_form (
	required_form_id bigint NOT NULL,
	form_status_code varchar(15) NOT NULL,
	service_event_id bigint NOT NULL,
	created_date timestamp(0) NOT NULL,
	created_by_login_id varchar(30) NOT NULL,
	last_modified_date timestamp(0) NOT NULL,
	last_modified_by_login_id varchar(30) NOT NULL,
	deactivated_reason_code varchar(20),
	deactivator_login_id varchar(30),
	deactivated_date timestamp(0),
	row_version_num numeric(38) NOT NULL,
	dci_form_id integer NOT NULL,
	prior_deactivated_form_status varchar(15),
	pending_message_id bigint,
	archived_flag varchar(1),
	reason_for_arch varchar(50)
) ;
COMMENT ON TABLE required_form IS E'This entity records all of the forms that were determined to be required for a service event. The determination is done firdst by ILOG, which is then verified by the CSR.';
COMMENT ON COLUMN required_form.archived_flag IS E'a ''Y'' indicates the form has been archived by the user.';
COMMENT ON COLUMN required_form.created_by_login_id IS E'The login id of the person who created the form.';
COMMENT ON COLUMN required_form.created_date IS E'The date on which the Form was created.';
COMMENT ON COLUMN required_form.dci_form_id IS E'A unique key for the DCI Form table.';
COMMENT ON COLUMN required_form.deactivated_date IS E'The date on which the form was deactivated from a service event.';
COMMENT ON COLUMN required_form.deactivated_reason_code IS E'The reason someone deactivated this from being required for a service event.';
COMMENT ON COLUMN required_form.deactivator_login_id IS E'The person who deactivated this form from a service event.';
COMMENT ON COLUMN required_form.form_status_code IS E'The status of the form for this service event. Valid values are: Not Started, In process, Signed, De-activated.';
COMMENT ON COLUMN required_form.last_modified_by_login_id IS E'The login id of the person who last modified the form.';
COMMENT ON COLUMN required_form.last_modified_date IS E'The date on which the Form was last modified.';
COMMENT ON COLUMN required_form.prior_deactivated_form_status IS E'The status of the form for this service event. Valid values are: Not Started, In process, Signed, De-activated.';
COMMENT ON COLUMN required_form.reason_for_arch IS E'Indicates the reason the user archived this form.';
COMMENT ON COLUMN required_form.required_form_id IS E'A unique key for the Form Content table, which is automatically generated by the database.';
COMMENT ON COLUMN required_form.row_version_num IS E'Facilitates hibernate optimistic locking.';
COMMENT ON COLUMN required_form.service_event_id IS E'A unique for the Service event, which is automatically generated by the database.';
CREATE INDEX required_form_fk1 ON required_form (service_event_id);
CREATE INDEX required_form_fk2 ON required_form (dci_form_id);
ALTER TABLE required_form ADD PRIMARY KEY (required_form_id);
ALTER TABLE required_form ALTER COLUMN required_form_id SET NOT NULL;
ALTER TABLE required_form ALTER COLUMN form_status_code SET NOT NULL;
ALTER TABLE required_form ALTER COLUMN service_event_id SET NOT NULL;
ALTER TABLE required_form ALTER COLUMN created_date SET NOT NULL;
ALTER TABLE required_form ALTER COLUMN created_by_login_id SET NOT NULL;
ALTER TABLE required_form ALTER COLUMN last_modified_date SET NOT NULL;
ALTER TABLE required_form ALTER COLUMN last_modified_by_login_id SET NOT NULL;
ALTER TABLE required_form ALTER COLUMN row_version_num SET NOT NULL;
ALTER TABLE required_form ALTER COLUMN dci_form_id SET NOT NULL;


CREATE TABLE required_form_content (
	required_form_content_id bigint NOT NULL,
	form_content text NOT NULL,
	required_form_id bigint NOT NULL,
	row_version_num numeric(38) NOT NULL
) ;
COMMENT ON COLUMN required_form_content.form_content IS E'The Form Content table contains the XML for the form.';
COMMENT ON COLUMN required_form_content.required_form_id IS E'A unique key for the Form Content table, which is automatically generated by the database.';
COMMENT ON COLUMN required_form_content.row_version_num IS E'Facilitates hibernate optimistic locking.';
CREATE INDEX required_form_content_fk1 ON required_form_content (required_form_id);
ALTER TABLE required_form_content ADD PRIMARY KEY (required_form_content_id);
ALTER TABLE required_form_content ALTER COLUMN required_form_content_id SET NOT NULL;
ALTER TABLE required_form_content ALTER COLUMN form_content SET NOT NULL;
ALTER TABLE required_form_content ALTER COLUMN required_form_id SET NOT NULL;
ALTER TABLE required_form_content ALTER COLUMN row_version_num SET NOT NULL;


CREATE TABLE response_option (
	response_option_id integer NOT NULL,
	option_name varchar(40) NOT NULL
) ;
ALTER TABLE response_option ADD PRIMARY KEY (response_option_id);
ALTER TABLE response_option ALTER COLUMN response_option_id SET NOT NULL;
ALTER TABLE response_option ALTER COLUMN option_name SET NOT NULL;


CREATE TABLE safety_vlv_accptbl_prssr_rng (
	nominal_std decimal(5,2) NOT NULL,
	min_vtp double precision NOT NULL,
	max_vtp double precision NOT NULL,
	min_std double precision NOT NULL,
	max_std double precision NOT NULL
) ;
ALTER TABLE safety_vlv_accptbl_prssr_rng ADD PRIMARY KEY (nominal_std);
ALTER TABLE safety_vlv_accptbl_prssr_rng ALTER COLUMN nominal_std SET NOT NULL;
ALTER TABLE safety_vlv_accptbl_prssr_rng ALTER COLUMN min_vtp SET NOT NULL;
ALTER TABLE safety_vlv_accptbl_prssr_rng ALTER COLUMN max_vtp SET NOT NULL;
ALTER TABLE safety_vlv_accptbl_prssr_rng ALTER COLUMN min_std SET NOT NULL;
ALTER TABLE safety_vlv_accptbl_prssr_rng ALTER COLUMN max_std SET NOT NULL;


CREATE TABLE seal_material (
	seal_mtrl_id integer NOT NULL,
	seal_mtrl_name varchar(30) NOT NULL,
	dropdown_source_id integer NOT NULL DEFAULT 1,
	seal_mtrl_code varchar(10)
) ;
COMMENT ON COLUMN seal_material.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
COMMENT ON COLUMN seal_material.seal_mtrl_code IS E'The code for the Seal Material.';
COMMENT ON COLUMN seal_material.seal_mtrl_name IS E'The material the seal is made of.';
ALTER TABLE seal_material ADD PRIMARY KEY (seal_mtrl_id);
ALTER TABLE seal_material ALTER COLUMN seal_mtrl_id SET NOT NULL;
ALTER TABLE seal_material ALTER COLUMN seal_mtrl_name SET NOT NULL;
ALTER TABLE seal_material ALTER COLUMN dropdown_source_id SET NOT NULL;


CREATE TABLE secondary_tcid_pattern_source (
	interior_exterior_id smallint NOT NULL,
	locn_group_id smallint NOT NULL,
	tcid_component_id integer,
	dropdown_source_id integer NOT NULL
) ;
COMMENT ON COLUMN secondary_tcid_pattern_source.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
COMMENT ON COLUMN secondary_tcid_pattern_source.locn_group_id IS E'A unique identifier for a Location Group.';
CREATE INDEX sec_tcid_pattern_src_fk_02 ON secondary_tcid_pattern_source (interior_exterior_id);
CREATE INDEX sec_tcid_pattern_src_fk_03 ON secondary_tcid_pattern_source (tcid_component_id);
CREATE INDEX sec_tcid_pattern_src_fk_04 ON secondary_tcid_pattern_source (locn_group_id);
ALTER TABLE secondary_tcid_pattern_source ADD PRIMARY KEY (interior_exterior_id,locn_group_id);
ALTER TABLE secondary_tcid_pattern_source ALTER COLUMN interior_exterior_id SET NOT NULL;
ALTER TABLE secondary_tcid_pattern_source ALTER COLUMN locn_group_id SET NOT NULL;
ALTER TABLE secondary_tcid_pattern_source ALTER COLUMN dropdown_source_id SET NOT NULL;


CREATE TABLE service_event (
	service_event_id bigint NOT NULL,
	decal_lst_rpt_as_of_inbnd_date timestamp(0),
	decal_upn_cmplt_as_of_otb_date timestamp(0),
	form_finalized_by_login_id varchar(30),
	form_finalized_date timestamp(0),
	confirmed_stcc_code varchar(7),
	future_stcc_code varchar(7),
	confirmed_stcc_corrosive_ind varchar(1),
	future_stcc_corrosive_ind varchar(1),
	confirmed_stcc_name varchar(40),
	future_stcc_name varchar(40),
	hold_finalize_flag varchar(1) NOT NULL DEFAULT 'N',
	forms_validated_flag varchar(1) NOT NULL DEFAULT 'N',
	forms_validation_warning_flag varchar(1) NOT NULL DEFAULT 'N'
) ;
COMMENT ON TABLE service_event IS E'Service Event is used to record when the Forms have been declared ''Final''.';
COMMENT ON COLUMN service_event.confirmed_stcc_corrosive_ind IS E'Indicates if the confirmed commodity is corrosive to the tank material of this railcar.';
COMMENT ON COLUMN service_event.form_finalized_by_login_id IS E'The Login ID of the person who made the forms finalized.';
COMMENT ON COLUMN service_event.form_finalized_date IS E'The date on which the forms for the service event were final and can no longer be modified. ';
COMMENT ON COLUMN service_event.forms_validation_warning_flag IS E'This indicates when the Validate Forms action returns an error response for the service event.';
COMMENT ON COLUMN service_event.future_stcc_corrosive_ind IS E'Indicates if the future commodity is corrosive to the tank material of this railcar.';
COMMENT ON COLUMN service_event.service_event_id IS E'A unique for the Service event, which is automatically generated by the database.';
ALTER TABLE service_event ADD PRIMARY KEY (service_event_id);
ALTER TABLE service_event ALTER COLUMN service_event_id SET NOT NULL;
ALTER TABLE service_event ALTER COLUMN hold_finalize_flag SET NOT NULL;
ALTER TABLE service_event ALTER COLUMN forms_validated_flag SET NOT NULL;
ALTER TABLE service_event ALTER COLUMN forms_validation_warning_flag SET NOT NULL;


CREATE TABLE service_event_required_program (
	service_event_required_prog_id bigint NOT NULL,
	service_event_id bigint NOT NULL,
	program_type_code varchar(2) NOT NULL,
	program_num bigint NOT NULL,
	program_rvsn_code varchar(1),
	conditional_source_flag char(1)
) ;
COMMENT ON TABLE service_event_required_program IS E'this shows in the Instructions tab in Shop Portal.
This includes all programs required for the Service event, regardless of how the program was generated or if it has required dci.';
COMMENT ON COLUMN service_event_required_program.conditional_source_flag IS E'This flag will be a Y if the program was added by a condition of a form instead of as part of the initial programs for the service event.';
COMMENT ON COLUMN service_event_required_program.program_num IS E'Program Number is one of three colums that uniquely define a maintenance program.';
COMMENT ON COLUMN service_event_required_program.program_rvsn_code IS E'Program Revision Code is one of three colums that uniquely define a maintenance program.';
COMMENT ON COLUMN service_event_required_program.program_type_code IS E'Program Type Code is one of three colums that uniquely define a maintenance program.';
ALTER TABLE service_event_required_program ADD PRIMARY KEY (service_event_required_prog_id);
ALTER TABLE service_event_required_program ALTER COLUMN service_event_required_prog_id SET NOT NULL;
ALTER TABLE service_event_required_program ALTER COLUMN service_event_id SET NOT NULL;
ALTER TABLE service_event_required_program ALTER COLUMN program_type_code SET NOT NULL;
ALTER TABLE service_event_required_program ALTER COLUMN program_num SET NOT NULL;


CREATE TABLE shape (
	shape_id integer NOT NULL,
	mfact_model_no_id integer,
	shape varchar(30)
) ;
COMMENT ON TABLE shape IS E'When this table was implemented, it is only used for Hatches';
CREATE INDEX shape_if1 ON shape (mfact_model_no_id);
ALTER TABLE shape ADD PRIMARY KEY (shape_id);
ALTER TABLE shape ALTER COLUMN shape_id SET NOT NULL;


CREATE TABLE shop_portal_version (
	shop_portal_ver varchar(10) NOT NULL,
	eff_date timestamp(0) NOT NULL
) ;
ALTER TABLE shop_portal_version ADD PRIMARY KEY (shop_portal_ver);
ALTER TABLE shop_portal_version ALTER COLUMN shop_portal_ver SET NOT NULL;
ALTER TABLE shop_portal_version ALTER COLUMN eff_date SET NOT NULL;


CREATE TABLE source_dci (
	required_dci_id bigint NOT NULL,
	source_dci_id bigint NOT NULL,
	triggering_dci_id bigint NOT NULL
) ;
COMMENT ON COLUMN source_dci.required_dci_id IS E'A unique primary key for the required DCI for this form.
';
COMMENT ON COLUMN source_dci.source_dci_id IS E'The unique identifier for the DCI that cuased this DCI to be required for this service event.';
COMMENT ON COLUMN source_dci.triggering_dci_id IS E'A unique primary key for the required DCI for this form.';
CREATE INDEX source_dci_fk2 ON source_dci (triggering_dci_id);
CREATE INDEX source_dci_fk3 ON source_dci (required_dci_id);
ALTER TABLE source_dci ADD PRIMARY KEY (source_dci_id);
ALTER TABLE source_dci ALTER COLUMN required_dci_id SET NOT NULL;
ALTER TABLE source_dci ALTER COLUMN source_dci_id SET NOT NULL;
ALTER TABLE source_dci ALTER COLUMN triggering_dci_id SET NOT NULL;


CREATE TABLE source_dci_detail (
	source_dci_detail_id bigint NOT NULL,
	source_dci_id bigint NOT NULL,
	detail_row_num smallint NOT NULL
) ;
COMMENT ON COLUMN source_dci_detail.source_dci_id IS E'The unique identifier for the DCI that cuased this DCI to be required for this service event.';
CREATE INDEX source_dci_detail_fk1 ON source_dci_detail (source_dci_id);
ALTER TABLE source_dci_detail ADD PRIMARY KEY (source_dci_detail_id);
ALTER TABLE source_dci_detail ALTER COLUMN source_dci_id SET NOT NULL;
ALTER TABLE source_dci_detail ALTER COLUMN detail_row_num SET NOT NULL;
ALTER TABLE source_dci_detail ALTER COLUMN source_dci_detail_id SET NOT NULL;


CREATE TABLE surface_temperature_range (
	surface_temp_range_id smallint NOT NULL,
	test_solution varchar(20),
	min_temp integer,
	max_temp integer,
	test_type varchar(3)
) ;
COMMENT ON TABLE surface_temperature_range IS E'This table holds the minimum and mazimum surface temperatures based on the test solution used.';
COMMENT ON COLUMN surface_temperature_range.max_temp IS E'The maximum temperature allowed for this solution used in the test.';
COMMENT ON COLUMN surface_temperature_range.min_temp IS E'The minimum temperature allowed for this solution used in the test.';
COMMENT ON COLUMN surface_temperature_range.surface_temp_range_id IS E'surrogate key';
COMMENT ON COLUMN surface_temperature_range.test_solution IS E'The solution used to test the surface temperature of a tankcar.';
COMMENT ON COLUMN surface_temperature_range.test_type IS E'The type of test used for testing fittings on service equipment. Examples are LT (leak test), BT (bench test).';
ALTER TABLE surface_temperature_range ADD PRIMARY KEY (surface_temp_range_id);
ALTER TABLE surface_temperature_range ALTER COLUMN surface_temp_range_id SET NOT NULL;


CREATE TABLE sys_export_table_01 (
	process_order bigint,
	duplicate bigint,
	dump_fileid bigint,
	dump_position bigint,
	dump_length bigint,
	dump_orig_length bigint,
	dump_allocation bigint,
	completed_rows bigint,
	error_count bigint,
	elapsed_time bigint,
	object_type_path varchar(200),
	object_path_seqno bigint,
	object_type varchar(30),
	in_progress char(1),
	object_name varchar(500),
	object_long_name varchar(4000),
	object_schema varchar(30),
	original_object_schema varchar(30),
	original_object_name varchar(4000),
	partition_name varchar(30),
	subpartition_name varchar(30),
	dataobj_num bigint,
	flags bigint,
	property bigint,
	trigflag bigint,
	creation_level bigint,
	completion_time timestamp(0),
	object_tablespace varchar(30),
	size_estimate bigint,
	object_row bigint,
	processing_state char(1),
	processing_status char(1),
	base_process_order bigint,
	base_object_type varchar(30),
	base_object_name varchar(30),
	base_object_schema varchar(30),
	ancestor_process_order bigint,
	domain_process_order bigint,
	parallelization bigint,
	unload_method bigint,
	load_method bigint,
	granules bigint,
	scn bigint,
	grantor varchar(30),
	xml_clob text,
	parent_process_order bigint,
	name varchar(30),
	value_t varchar(4000),
	value_n bigint,
	is_default bigint,
	file_type bigint,
	user_directory varchar(4000),
	user_file_name varchar(4000),
	file_name varchar(4000),
	extend_size bigint,
	file_max_size bigint,
	process_name varchar(30),
	last_update timestamp(0),
	work_item varchar(30),
	object_number bigint,
	completed_bytes bigint,
	total_bytes bigint,
	metadata_io bigint,
	data_io bigint,
	cumulative_time bigint,
	packet_number bigint,
	instance_id bigint,
	old_value varchar(4000),
	seed bigint,
	last_file bigint,
	user_name varchar(30),
	operation varchar(30),
	job_mode varchar(30),
	queue_tabnum bigint,
	control_queue varchar(30),
	status_queue varchar(30),
	remote_link varchar(4000),
	version bigint,
	job_version varchar(30),
	db_version varchar(30),
	timezone varchar(64),
	state varchar(30),
	phase bigint,
	guid uuid,
	start_time timestamp(0),
	block_size bigint,
	metadata_buffer_size bigint,
	data_buffer_size bigint,
	degree bigint,
	platform varchar(101),
	abort_step bigint,
	instance varchar(60),
	cluster_ok bigint,
	service_name varchar(100),
	object_int_oid varchar(32)
) ;
COMMENT ON TABLE sys_export_table_01 IS E'Data Pump Master Table EXPORT                         TABLE                         ';
CREATE INDEX sys_mtable_0000fdf70_ind_1 ON sys_export_table_01 (object_schema, object_name, object_type);
CREATE INDEX sys_mtable_0000fdf70_ind_2 ON sys_export_table_01 (base_process_order);
ALTER TABLE sys_export_table_01 ADD UNIQUE (process_order,duplicate);


CREATE TABLE tcid_alterations_mapping (
	tcid_value_lookup_code varchar(4) NOT NULL,
	tcid_chng_catg varchar(20),
	aar_approval_ref varchar(10),
	draw_part_doc_desc varchar(50)
) ;
ALTER TABLE tcid_alterations_mapping ADD PRIMARY KEY (tcid_value_lookup_code);
ALTER TABLE tcid_alterations_mapping ALTER COLUMN tcid_value_lookup_code SET NOT NULL;


CREATE TABLE tcid_compnt_defect_orientation (
	tcid_component_id integer NOT NULL,
	tcid_defect_orientation_id integer NOT NULL,
	eff_date timestamp(0),
	expiration_date timestamp(0)
) ;
CREATE INDEX tcid_compt_dfct_orientn_fk01 ON tcid_compnt_defect_orientation (tcid_defect_orientation_id);
CREATE INDEX tcid_compt_dfct_orientn_fk02 ON tcid_compnt_defect_orientation (tcid_component_id);
ALTER TABLE tcid_compnt_defect_orientation ADD PRIMARY KEY (tcid_component_id,tcid_defect_orientation_id);
ALTER TABLE tcid_compnt_defect_orientation ALTER COLUMN tcid_component_id SET NOT NULL;
ALTER TABLE tcid_compnt_defect_orientation ALTER COLUMN tcid_defect_orientation_id SET NOT NULL;


CREATE TABLE tcid_component (
	tcid_component_id integer NOT NULL,
	tcid_component_name varchar(50) NOT NULL,
	eff_date timestamp(0),
	expiration_date timestamp(0)
) ;
ALTER TABLE tcid_component ADD PRIMARY KEY (tcid_component_id);
ALTER TABLE tcid_component ALTER COLUMN tcid_component_id SET NOT NULL;
ALTER TABLE tcid_component ALTER COLUMN tcid_component_name SET NOT NULL;


CREATE TABLE tcid_component_defect_cause (
	tcid_component_id integer NOT NULL,
	tcid_defect_cause_id integer NOT NULL,
	eff_date timestamp(0),
	expiration_date timestamp(0)
) ;
CREATE INDEX tcid_comp_defect_cause_fk1 ON tcid_component_defect_cause (tcid_component_id);
CREATE INDEX tcid_comp_defect_cause_fk2 ON tcid_component_defect_cause (tcid_defect_cause_id);
ALTER TABLE tcid_component_defect_cause ADD PRIMARY KEY (tcid_component_id,tcid_defect_cause_id);
ALTER TABLE tcid_component_defect_cause ALTER COLUMN tcid_component_id SET NOT NULL;
ALTER TABLE tcid_component_defect_cause ALTER COLUMN tcid_defect_cause_id SET NOT NULL;


CREATE TABLE tcid_component_defect_type (
	tcid_component_id integer NOT NULL,
	tcid_defect_type_id integer NOT NULL,
	eff_date timestamp(0),
	expiration_date timestamp(0)
) ;
CREATE INDEX tcid_component_defect_type_fk1 ON tcid_component_defect_type (tcid_component_id);
CREATE INDEX tcid_component_defect_type_fk2 ON tcid_component_defect_type (tcid_defect_type_id);
ALTER TABLE tcid_component_defect_type ADD PRIMARY KEY (tcid_component_id,tcid_defect_type_id);
ALTER TABLE tcid_component_defect_type ALTER COLUMN tcid_component_id SET NOT NULL;
ALTER TABLE tcid_component_defect_type ALTER COLUMN tcid_defect_type_id SET NOT NULL;


CREATE TABLE tcid_component_inspection_meth (
	tcid_component_id integer NOT NULL,
	tcid_insp_method_id integer NOT NULL,
	eff_date timestamp(0),
	expiration_date timestamp(0)
) ;
CREATE INDEX tcid_comp_inspection_meth_fk1 ON tcid_component_inspection_meth (tcid_component_id);
CREATE INDEX tcid_comp_inspection_meth_fk2 ON tcid_component_inspection_meth (tcid_insp_method_id);
ALTER TABLE tcid_component_inspection_meth ADD PRIMARY KEY (tcid_component_id,tcid_insp_method_id);
ALTER TABLE tcid_component_inspection_meth ALTER COLUMN tcid_component_id SET NOT NULL;
ALTER TABLE tcid_component_inspection_meth ALTER COLUMN tcid_insp_method_id SET NOT NULL;


CREATE TABLE tcid_component_location (
	tcid_component_id integer NOT NULL,
	tcid_locn_id integer NOT NULL,
	eff_date timestamp(0),
	expiration_date timestamp(0)
) ;
CREATE INDEX tcid_component_location_fk1 ON tcid_component_location (tcid_component_id);
CREATE INDEX tcid_component_location_fk2 ON tcid_component_location (tcid_locn_id);
ALTER TABLE tcid_component_location ADD PRIMARY KEY (tcid_component_id,tcid_locn_id);
ALTER TABLE tcid_component_location ALTER COLUMN tcid_component_id SET NOT NULL;
ALTER TABLE tcid_component_location ALTER COLUMN tcid_locn_id SET NOT NULL;


CREATE TABLE tcid_component_repair_method (
	tcid_component_id integer NOT NULL,
	tcid_repair_method_id integer NOT NULL,
	eff_date timestamp(0),
	expiration_date timestamp(0)
) ;
CREATE INDEX tcid_comp_repair_method_fk1 ON tcid_component_repair_method (tcid_component_id);
CREATE INDEX tcid_comp_repair_method_fk2 ON tcid_component_repair_method (tcid_repair_method_id);
ALTER TABLE tcid_component_repair_method ADD PRIMARY KEY (tcid_component_id,tcid_repair_method_id);
ALTER TABLE tcid_component_repair_method ALTER COLUMN tcid_component_id SET NOT NULL;
ALTER TABLE tcid_component_repair_method ALTER COLUMN tcid_repair_method_id SET NOT NULL;


CREATE TABLE tcid_defect_cause (
	tcid_defect_cause_id integer NOT NULL,
	defect_cause_name varchar(50) NOT NULL,
	tcid_desc_id integer
) ;
COMMENT ON COLUMN tcid_defect_cause.tcid_desc_id IS E'A surrogate key to uniquely identify a row.';
CREATE INDEX tcid_defect_cause_fk1 ON tcid_defect_cause (tcid_desc_id);
ALTER TABLE tcid_defect_cause ADD PRIMARY KEY (tcid_defect_cause_id);
ALTER TABLE tcid_defect_cause ALTER COLUMN tcid_defect_cause_id SET NOT NULL;
ALTER TABLE tcid_defect_cause ALTER COLUMN defect_cause_name SET NOT NULL;


CREATE TABLE tcid_defect_indicator (
	tcid_defect_indicator_id integer NOT NULL,
	tcid_defect_indicator_code varchar(5),
	tcid_defect_indicator_desc varchar(30)
) ;
ALTER TABLE tcid_defect_indicator ADD PRIMARY KEY (tcid_defect_indicator_id);
ALTER TABLE tcid_defect_indicator ALTER COLUMN tcid_defect_indicator_id SET NOT NULL;


CREATE TABLE tcid_defect_orientation (
	tcid_defect_orientation_id integer NOT NULL,
	tcid_desc_id integer,
	tcid_defect_orientation_code varchar(5),
	tcid_defect_orientation_name varchar(50)
) ;
COMMENT ON COLUMN tcid_defect_orientation.tcid_desc_id IS E'A surrogate key to uniquely identify a row.';
CREATE INDEX tcid_defect_orientation_fk01 ON tcid_defect_orientation (tcid_desc_id);
ALTER TABLE tcid_defect_orientation ADD PRIMARY KEY (tcid_defect_orientation_id);
ALTER TABLE tcid_defect_orientation ALTER COLUMN tcid_defect_orientation_id SET NOT NULL;


CREATE TABLE tcid_defect_type (
	tcid_defect_type_id integer NOT NULL,
	tcid_defect_type_name varchar(50) NOT NULL,
	tcid_desc_id integer,
	tcid_defect_indicator_id integer,
	defect_measurement_type varchar(1)
) ;
COMMENT ON COLUMN tcid_defect_type.defect_measurement_type IS E'Indicates whether the measurement is for the length or the area of the defect. (A or L)';
COMMENT ON COLUMN tcid_defect_type.tcid_desc_id IS E'A surrogate key to uniquely identify a row.';
CREATE INDEX tcid_defect_type_fk1 ON tcid_defect_type (tcid_desc_id);
CREATE INDEX tcid_defect_type_fk2 ON tcid_defect_type (tcid_defect_indicator_id);
ALTER TABLE tcid_defect_type ADD PRIMARY KEY (tcid_defect_type_id);
ALTER TABLE tcid_defect_type ALTER COLUMN tcid_defect_type_id SET NOT NULL;
ALTER TABLE tcid_defect_type ALTER COLUMN tcid_defect_type_name SET NOT NULL;


CREATE TABLE tcid_description (
	tcid_desc_id integer NOT NULL,
	tcid_desc_type varchar(30),
	tcid_value varchar(70),
	tcid_desc varchar(100),
	gatx_desc_type varchar(30)
) ;
COMMENT ON COLUMN tcid_description.gatx_desc_type IS E'Identifies which GATX dropdown column these values refer to.';
COMMENT ON COLUMN tcid_description.tcid_desc IS E'The TCID Description column is the descriptive information we received in the TCID manual to further describe the TCID Value.';
COMMENT ON COLUMN tcid_description.tcid_desc_id IS E'A surrogate key to uniquely identify a row.';
COMMENT ON COLUMN tcid_description.tcid_desc_type IS E'The TCID Description Type column identifies the domain of values. (Examples are Defect Type, Defect Cause, Inspection Method, Repair Method, Location)';
COMMENT ON COLUMN tcid_description.tcid_value IS E'The TCID Value column is the information we send to TCID.';
ALTER TABLE tcid_description ADD PRIMARY KEY (tcid_desc_id);
ALTER TABLE tcid_description ALTER COLUMN tcid_desc_id SET NOT NULL;


CREATE TABLE tcid_inspection_method (
	tcid_insp_method_id integer NOT NULL,
	insp_method_name varchar(50) NOT NULL,
	tcid_desc_id integer
) ;
COMMENT ON COLUMN tcid_inspection_method.tcid_desc_id IS E'A surrogate key to uniquely identify a row.';
CREATE INDEX tcid_inspection_method_fk1 ON tcid_inspection_method (tcid_desc_id);
ALTER TABLE tcid_inspection_method ADD PRIMARY KEY (tcid_insp_method_id);
ALTER TABLE tcid_inspection_method ALTER COLUMN tcid_insp_method_id SET NOT NULL;
ALTER TABLE tcid_inspection_method ALTER COLUMN insp_method_name SET NOT NULL;


CREATE TABLE tcid_location (
	tcid_locn_id integer NOT NULL,
	ring_qty smallint,
	locn_name varchar(50) NOT NULL,
	tcid_desc_id integer
) ;
COMMENT ON COLUMN tcid_location.tcid_desc_id IS E'A surrogate key to uniquely identify a row.';
CREATE INDEX tcid_location_fk1 ON tcid_location (tcid_desc_id);
ALTER TABLE tcid_location ADD PRIMARY KEY (tcid_locn_id);
ALTER TABLE tcid_location ALTER COLUMN tcid_locn_id SET NOT NULL;
ALTER TABLE tcid_location ALTER COLUMN locn_name SET NOT NULL;


CREATE TABLE tcid_repair_method (
	tcid_repair_method_id integer NOT NULL,
	repair_method_name varchar(70) NOT NULL,
	tcid_desc_id integer
) ;
COMMENT ON COLUMN tcid_repair_method.tcid_desc_id IS E'A surrogate key to uniquely identify a row.';
CREATE INDEX tcid_repair_method_fk1 ON tcid_repair_method (tcid_desc_id);
ALTER TABLE tcid_repair_method ADD PRIMARY KEY (tcid_repair_method_id);
ALTER TABLE tcid_repair_method ALTER COLUMN tcid_repair_method_id SET NOT NULL;
ALTER TABLE tcid_repair_method ALTER COLUMN repair_method_name SET NOT NULL;


CREATE TABLE thickness_valid_percentage (
	locn_group_id smallint NOT NULL,
	as_received_percentage decimal(5,2) NOT NULL,
	after_repair_percentage decimal(5,2) NOT NULL
) ;
COMMENT ON COLUMN thickness_valid_percentage.locn_group_id IS E'A unique identifier for a Location Group.';
ALTER TABLE thickness_valid_percentage ADD PRIMARY KEY (locn_group_id);
ALTER TABLE thickness_valid_percentage ALTER COLUMN locn_group_id SET NOT NULL;
ALTER TABLE thickness_valid_percentage ALTER COLUMN as_received_percentage SET NOT NULL;
ALTER TABLE thickness_valid_percentage ALTER COLUMN after_repair_percentage SET NOT NULL;


CREATE TABLE torque_wrench (
	torque_wrench_id integer NOT NULL,
	liner_type_code varchar(1),
	fastener_size varchar(15),
	socket_min_qty smallint,
	socket_max_qty smallint,
	crow_ft_min_qty smallint,
	crow_ft_max_qty smallint,
	washer_type varchar(2)
) ;
COMMENT ON TABLE torque_wrench IS E'This table was created for Story 8048 for Shop Portal and is used for recommended torque values on the F03b-final assembly form.';
COMMENT ON COLUMN torque_wrench.fastener_size IS E'This column is for the fastener size in inches for the recommended torque values.';
COMMENT ON COLUMN torque_wrench.liner_type_code IS E'Either Spray Lined or Rubber Lined car';
COMMENT ON COLUMN torque_wrench.washer_type IS E'NL = Nordlock. Null means no washer.';
ALTER TABLE torque_wrench ADD PRIMARY KEY (torque_wrench_id);
ALTER TABLE torque_wrench ALTER COLUMN torque_wrench_id SET NOT NULL;


CREATE TABLE validation_description (
	dci_question_id integer,
	portal_catg varchar(40),
	portal_subcategory varchar(40),
	portal_display_name varchar(200),
	sequence_num smallint
) ;
COMMENT ON COLUMN validation_description.portal_catg IS E'The Portal Category used in the Shop Car Spec Sheet view.';
COMMENT ON COLUMN validation_description.portal_display_name IS E'The name of the attribute used in the Shop Car Spec Sheet view.';
COMMENT ON COLUMN validation_description.portal_subcategory IS E'The Portal Subcategory used in the Shop Car Spec Sheet view.';
COMMENT ON COLUMN validation_description.sequence_num IS E'The order this attribute will be in the concatenated description.';
CREATE INDEX validation_description_fk1 ON validation_description (dci_question_id);


CREATE TABLE valve_type (
	valve_type_id integer NOT NULL,
	mfact_model_no_id integer,
	valve_type varchar(30)
) ;
CREATE INDEX valve_type_if1 ON valve_type (mfact_model_no_id);
ALTER TABLE valve_type ADD PRIMARY KEY (valve_type_id);
ALTER TABLE valve_type ALTER COLUMN valve_type_id SET NOT NULL;


CREATE TABLE why_made (
	why_made_id integer NOT NULL,
	why_made_code varchar(2) NOT NULL,
	why_made_name varchar(120) NOT NULL,
	insp_point_id integer,
	dropdown_source_id integer
) ;
COMMENT ON COLUMN why_made.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
CREATE INDEX why_made_fk01 ON why_made (insp_point_id);
ALTER TABLE why_made ADD PRIMARY KEY (why_made_id);
ALTER TABLE why_made ALTER COLUMN why_made_id SET NOT NULL;
ALTER TABLE why_made ALTER COLUMN why_made_code SET NOT NULL;
ALTER TABLE why_made ALTER COLUMN why_made_name SET NOT NULL;


CREATE TABLE zone_location (
	zone_locn_code varchar(2) NOT NULL,
	zone_locn_code_name varchar(20) NOT NULL,
	interior_exterior_id smallint NOT NULL
) ;
CREATE INDEX zone_location_fk1 ON zone_location (interior_exterior_id);
ALTER TABLE zone_location ADD PRIMARY KEY (zone_locn_code);
ALTER TABLE zone_location ALTER COLUMN zone_locn_code SET NOT NULL;
ALTER TABLE zone_location ALTER COLUMN zone_locn_code_name SET NOT NULL;
ALTER TABLE zone_location ALTER COLUMN interior_exterior_id SET NOT NULL;


CREATE TABLE zone_location_dropdown (
	dropdown_source_id integer NOT NULL,
	zone_locn_code varchar(2) NOT NULL
) ;
COMMENT ON COLUMN zone_location_dropdown.dropdown_source_id IS E'This identifier is used in various tables for retrieve values for dropdown that are related to a specific DCI.';
COMMENT ON COLUMN zone_location_dropdown.zone_locn_code IS E'A location on the railcar used to segment the Inbound Inspection process to use multiple forms. (Inbound Inpsection form is the only form using the Zone Location Code at this point in time.)';
CREATE INDEX zone_location_dropdown_fk01 ON zone_location_dropdown (zone_locn_code);
ALTER TABLE zone_location_dropdown ADD PRIMARY KEY (dropdown_source_id,zone_locn_code);
ALTER TABLE zone_location_dropdown ALTER COLUMN dropdown_source_id SET NOT NULL;
ALTER TABLE zone_location_dropdown ALTER COLUMN zone_locn_code SET NOT NULL;
ALTER TABLE allowable_thickness_reduction ADD CONSTRAINT corrtype_2_allthickred_const01 FOREIGN KEY (corrosion_type_id) REFERENCES corrosion_type(corrosion_type_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE allowable_thickness_reduction ADD CONSTRAINT locngrp_2_allthickreduc_cons01 FOREIGN KEY (locn_group_id) REFERENCES location_group(locn_group_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE attribute_zone_xref ADD CONSTRAINT zonelocn_2_attribzonexref_con1 FOREIGN KEY (zone_locn_code) REFERENCES zone_location(zone_locn_code) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE cid_umler_cross_reference ADD CONSTRAINT cidmodapnm_2_ciduxref_constr01 FOREIGN KEY (model_approval_num_id) REFERENCES cid_model_approval_number(model_approval_num_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE cid_umler_cross_reference ADD CONSTRAINT mfactmdno_2_ciduxref_constr01 FOREIGN KEY (mfact_model_no_id) REFERENCES manufacturer_model_no(mfact_model_no_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE cid_umler_oring_crossreference ADD CONSTRAINT cidurgv_2_ciduoringxref_const1 FOREIGN KEY (umler_registration_value_id) REFERENCES cid_umler_registration_value(umler_registration_value_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE cid_umler_oring_crossreference ADD CONSTRAINT slmtrl_2_ciduoringxref_constr1 FOREIGN KEY (seal_mtrl_id) REFERENCES seal_material(seal_mtrl_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE connection_type ADD CONSTRAINT mfactmodelno_2_conntype_cons01 FOREIGN KEY (mfact_model_no_id) REFERENCES manufacturer_model_no(mfact_model_no_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci ADD CONSTRAINT dcifrm_to_dci_constr1 FOREIGN KEY (dci_form_id) REFERENCES dci_form(dci_form_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci ADD CONSTRAINT dcigroup_2_dci_constr01 FOREIGN KEY (dci_group_id) REFERENCES dci_group(dci_group_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci ADD CONSTRAINT dciquest_to_dci_constr1 FOREIGN KEY (dci_question_id) REFERENCES dci_question(dci_question_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci ADD CONSTRAINT dcisection_2_dci_constr01 FOREIGN KEY (dci_section_id) REFERENCES dci_section(dci_section_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci ADD CONSTRAINT znlocn_2_dci_constr01 FOREIGN KEY (zone_locn_code) REFERENCES zone_location(zone_locn_code) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci_form ADD CONSTRAINT frmtmplt_to_dcifrm_constr1 FOREIGN KEY (form_template_id) REFERENCES form_template(form_template_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci_question ADD CONSTRAINT dciquesttype_2_dciquest_const1 FOREIGN KEY (dci_question_type_id) REFERENCES dci_question_type(dci_question_type_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci_question ADD CONSTRAINT tciddefid_2_dciquestion_cons01 FOREIGN KEY (tcid_defect_indicator_id) REFERENCES tcid_defect_indicator(tcid_defect_indicator_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci_question_attribute ADD CONSTRAINT dciquestion_2_dciquesattrib_c1 FOREIGN KEY (dci_question_id) REFERENCES dci_question(dci_question_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci_question_dropdown_source ADD CONSTRAINT dciquest_2_dciquestdsrc_const1 FOREIGN KEY (dci_question_id) REFERENCES dci_question(dci_question_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci_response_pattern_group ADD CONSTRAINT dciquest_2_dcirespatgroup_co01 FOREIGN KEY (dci_question_id) REFERENCES dci_question(dci_question_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci_response_pattern_group ADD CONSTRAINT patgroup_2_dcirespatgroup_co01 FOREIGN KEY (pattern_group_id) REFERENCES pattern_group(pattern_group_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci_response_pattern_group ADD CONSTRAINT patgroup_2_dcirespatgroup_co02 FOREIGN KEY (secondary_pattern_group_id) REFERENCES pattern_group(pattern_group_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dci_response_pattern_group ADD CONSTRAINT resopt_2_dcirespatgroup_cons01 FOREIGN KEY (response_option_id) REFERENCES response_option(response_option_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE defect ADD CONSTRAINT inspnt_to_defect_constr01 FOREIGN KEY (insp_point_id) REFERENCES inspection_point(insp_point_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dropdown_list_content ADD CONSTRAINT ddownlist_2_ddownlistcont_co01 FOREIGN KEY (dropdown_list_id) REFERENCES dropdown_list(dropdown_list_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE dropdown_list_content_level_2 ADD CONSTRAINT ddwnlstcont_2_ddwnlstcontl2_c1 FOREIGN KEY (dropdown_list_content_id) REFERENCES dropdown_list_content(dropdown_list_content_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE figure ADD CONSTRAINT refdoc_to_fig_constr01 FOREIGN KEY (reference_doc_id) REFERENCES reference_document(reference_doc_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE inspection_point_defect_rank ADD CONSTRAINT dciqst_to_insptdftrnk_constr01 FOREIGN KEY (dci_question_id) REFERENCES dci_question(dci_question_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE inspection_point_defect_rank ADD CONSTRAINT dfct_to_insptdftrnk_constr01 FOREIGN KEY (defect_code_id) REFERENCES defect(defect_code_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE inspection_point_defect_rank ADD CONSTRAINT inspnt_to_insptdftrnk_constr01 FOREIGN KEY (insp_point_id) REFERENCES inspection_point(insp_point_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE inspection_point_rank ADD CONSTRAINT dciqst_to_inspntrnk_constr01 FOREIGN KEY (dci_question_id) REFERENCES dci_question(dci_question_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE inspection_point_rank ADD CONSTRAINT inspnt_to_inspntrnk_constr01 FOREIGN KEY (insp_point_id) REFERENCES inspection_point(insp_point_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE inspection_point_zone_location ADD CONSTRAINT insppoint_2_inspptznlocn_con01 FOREIGN KEY (insp_point_id) REFERENCES inspection_point(insp_point_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE inspection_point_zone_location ADD CONSTRAINT znlocn_2_inspptznlocn_constr01 FOREIGN KEY (zone_locn_code) REFERENCES zone_location(zone_locn_code) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE job_code ADD CONSTRAINT insppnt_to_jobcd_constr1 FOREIGN KEY (insp_point_id) REFERENCES inspection_point(insp_point_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE location ADD CONSTRAINT locngroup_2_locn_constr01 FOREIGN KEY (location_group_id) REFERENCES location_group(locn_group_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE location_corrosion_type ADD CONSTRAINT corrtype_2_locncorrtype_cons01 FOREIGN KEY (corrosion_type_id) REFERENCES corrosion_type(corrosion_type_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE location_corrosion_type ADD CONSTRAINT locngrp_2_locncorrtype_const01 FOREIGN KEY (locn_group_id) REFERENCES location_group(locn_group_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE location_interior_exterior ADD CONSTRAINT intext_2_locnintext_constr01 FOREIGN KEY (interior_exterior_id) REFERENCES interior_exterior(interior_exterior_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE location_interior_exterior ADD CONSTRAINT locngrp_2_locnintext_constr01 FOREIGN KEY (locn_group_id) REFERENCES location_group(locn_group_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE manufacturer_model_no ADD CONSTRAINT mfact_2_mfactmodel_no_constr01 FOREIGN KEY (mfact_id) REFERENCES manufacturer(manufacturer_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE material ADD CONSTRAINT mfactmodelno_2_material_cons01 FOREIGN KEY (mfact_model_no_id) REFERENCES manufacturer_model_no(mfact_model_no_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE model_number_suffix ADD CONSTRAINT mfactmodelno_2_mdlnosfx_c1 FOREIGN KEY (mfact_model_no_id) REFERENCES manufacturer_model_no(mfact_model_no_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE model_size ADD CONSTRAINT mfactmodelno_2_modelsize_con01 FOREIGN KEY (mfact_model_no_id) REFERENCES manufacturer_model_no(mfact_model_no_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE model_size ADD CONSTRAINT shape_2_mdlsz_constr_01 FOREIGN KEY (shape_id) REFERENCES shape(shape_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE nozzle_condemnable_limit ADD CONSTRAINT locngrp_2_nozcond_lmt_constr01 FOREIGN KEY (locn_group_id) REFERENCES location_group(locn_group_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE pressure_rating ADD CONSTRAINT mfactmodelno_2_presrtng_const1 FOREIGN KEY (mfact_model_no_id) REFERENCES manufacturer_model_no(mfact_model_no_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE qualification_decal_last_rpt ADD CONSTRAINT srvev_to_qualdeclstrpt_constr1 FOREIGN KEY (service_event_id) REFERENCES service_event(service_event_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE qualifier ADD CONSTRAINT insppnt_to_qualif_constr1 FOREIGN KEY (insp_point_id) REFERENCES inspection_point(insp_point_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE required_dci ADD CONSTRAINT rqrdfrm_to_rqrddci_constr1 FOREIGN KEY (required_form_id) REFERENCES required_form(required_form_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE required_dci_program ADD CONSTRAINT reqdci_to_reqdciprgm_constr1 FOREIGN KEY (required_dci_id) REFERENCES required_dci(required_dci_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE required_dci_program ADD CONSTRAINT srvevrqpgm_to_rqdcipgm_constr1 FOREIGN KEY (service_event_required_prog_id) REFERENCES service_event_required_program(service_event_required_prog_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE required_form ADD CONSTRAINT dcifrm_to_rqrdfrm_constr01 FOREIGN KEY (dci_form_id) REFERENCES dci_form(dci_form_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE required_form ADD CONSTRAINT servevnt_to_rqrdfrm_const1 FOREIGN KEY (service_event_id) REFERENCES service_event(service_event_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE required_form_content ADD CONSTRAINT rqrdfrm_to_rqrfrmcntnt_constr1 FOREIGN KEY (required_form_id) REFERENCES required_form(required_form_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE secondary_tcid_pattern_source ADD CONSTRAINT intext_to_sctcidpsrc_constr01 FOREIGN KEY (interior_exterior_id) REFERENCES interior_exterior(interior_exterior_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE secondary_tcid_pattern_source ADD CONSTRAINT locngrp_to_sctcidpsrc_constr01 FOREIGN KEY (locn_group_id) REFERENCES location_group(locn_group_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE secondary_tcid_pattern_source ADD CONSTRAINT tcidcmp_to_sctcidpsrc_constr01 FOREIGN KEY (tcid_component_id) REFERENCES tcid_component(tcid_component_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE shape ADD CONSTRAINT mfactmodelno_2_shape_constr1 FOREIGN KEY (mfact_model_no_id) REFERENCES manufacturer_model_no(mfact_model_no_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE source_dci ADD CONSTRAINT reqdci_2_sourcedci_constr01 FOREIGN KEY (triggering_dci_id) REFERENCES required_dci(required_dci_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE source_dci ADD CONSTRAINT reqdci_2_sourcedci_constr02 FOREIGN KEY (required_dci_id) REFERENCES required_dci(required_dci_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE source_dci_detail ADD CONSTRAINT sourcedci_2_sourcedcidtl_con01 FOREIGN KEY (source_dci_id) REFERENCES source_dci(source_dci_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_compnt_defect_orientation ADD CONSTRAINT tcidcmpt_2_tcidcmpdfor_const1 FOREIGN KEY (tcid_component_id) REFERENCES tcid_component(tcid_component_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_compnt_defect_orientation ADD CONSTRAINT tciddfor_2_tcidcmpdfor_const1 FOREIGN KEY (tcid_defect_orientation_id) REFERENCES tcid_defect_orientation(tcid_defect_orientation_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_component_defect_cause ADD CONSTRAINT tcidcom_2_tcidcomdefcause_co01 FOREIGN KEY (tcid_component_id) REFERENCES tcid_component(tcid_component_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_component_defect_cause ADD CONSTRAINT tciddefcau_2_tcidcomdefcau_c01 FOREIGN KEY (tcid_defect_cause_id) REFERENCES tcid_defect_cause(tcid_defect_cause_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_component_defect_type ADD CONSTRAINT tcidcom_2_tcidcomdeftype_con01 FOREIGN KEY (tcid_component_id) REFERENCES tcid_component(tcid_component_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_component_defect_type ADD CONSTRAINT tciddefty_2_tcidcomdefty_con01 FOREIGN KEY (tcid_defect_type_id) REFERENCES tcid_defect_type(tcid_defect_type_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_component_inspection_meth ADD CONSTRAINT tcidcom_2_tcidcominsmeth_con01 FOREIGN KEY (tcid_component_id) REFERENCES tcid_component(tcid_component_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_component_inspection_meth ADD CONSTRAINT tcidinsme_2_tcidcominsme_con01 FOREIGN KEY (tcid_insp_method_id) REFERENCES tcid_inspection_method(tcid_insp_method_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_component_location ADD CONSTRAINT tcidcom_2_tcidcomlocn_constr01 FOREIGN KEY (tcid_component_id) REFERENCES tcid_component(tcid_component_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_component_location ADD CONSTRAINT tcidlocn_2_tcidcomlocn_const01 FOREIGN KEY (tcid_locn_id) REFERENCES tcid_location(tcid_locn_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_component_repair_method ADD CONSTRAINT tcidcom_2_tcidcomrepmeth_con01 FOREIGN KEY (tcid_component_id) REFERENCES tcid_component(tcid_component_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_component_repair_method ADD CONSTRAINT tcidrepme_2_tcidcomrepme_con01 FOREIGN KEY (tcid_repair_method_id) REFERENCES tcid_repair_method(tcid_repair_method_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_defect_cause ADD CONSTRAINT tciddesc_2_tciddefcause_cons01 FOREIGN KEY (tcid_desc_id) REFERENCES tcid_description(tcid_desc_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_defect_orientation ADD CONSTRAINT tciddesc_2_tciddfctorn_const1 FOREIGN KEY (tcid_desc_id) REFERENCES tcid_description(tcid_desc_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_defect_type ADD CONSTRAINT tciddefind_2_tciddeftype_con01 FOREIGN KEY (tcid_defect_indicator_id) REFERENCES tcid_defect_indicator(tcid_defect_indicator_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_defect_type ADD CONSTRAINT tciddesc_2_tciddeftype_const01 FOREIGN KEY (tcid_desc_id) REFERENCES tcid_description(tcid_desc_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_inspection_method ADD CONSTRAINT tciddesc_2_tcidinsmeth_const01 FOREIGN KEY (tcid_desc_id) REFERENCES tcid_description(tcid_desc_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_location ADD CONSTRAINT tciddesc_2_tcidlocn_constr01 FOREIGN KEY (tcid_desc_id) REFERENCES tcid_description(tcid_desc_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE tcid_repair_method ADD CONSTRAINT tciddesc_2_tcid_rp_meth_cons01 FOREIGN KEY (tcid_desc_id) REFERENCES tcid_description(tcid_desc_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE thickness_valid_percentage ADD CONSTRAINT locngrp_2_thickvalperct_cons01 FOREIGN KEY (locn_group_id) REFERENCES location_group(locn_group_id) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE validation_description ADD CONSTRAINT dciquestion_2_valdesc_const1 FOREIGN KEY (dci_question_id) REFERENCES dci_question(dci_question_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED NOT VALID;
ALTER TABLE valve_type ADD CONSTRAINT mfactmodelno_2_vlvtp_const1 FOREIGN KEY (mfact_model_no_id) REFERENCES manufacturer_model_no(mfact_model_no_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE why_made ADD CONSTRAINT insppnt_to_whymd_constr1 FOREIGN KEY (insp_point_id) REFERENCES inspection_point(insp_point_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE zone_location ADD CONSTRAINT intext_2_zonelocn_constr01 FOREIGN KEY (interior_exterior_id) REFERENCES interior_exterior(interior_exterior_id) ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE zone_location_dropdown ADD CONSTRAINT zonelocn_2_zonelocndrpdwn_con1 FOREIGN KEY (zone_locn_code) REFERENCES zone_location(zone_locn_code) ON DELETE NO ACTION DEFERRABLE INITIALLY DEFERRED;