import { readFileSync } from 'fs'
import datasource from '../datasource'

const ds = await datasource
await ds.initialize()

/**
 * Bootstrap function to initialize the database schema and views.
 * It reads SQL files and executes them using the TypeORM query runner.
 *
 * @param filename - The name of the SQL file to execute (without extension).
 */
export const bootstrap = async (filename: string) => {
  const sqlFilePath = `${__dirname}/queries/${filename}.sql`
  const sql = readFileSync(sqlFilePath, 'utf8')

  const runner = ds.createQueryRunner()
  await runner.startTransaction()
  try {
    // For more useful debugging, we run each statement in a file independently.
    for (const statement of sql.split(';\n')) {
      await runner.query(statement)
    }
    await runner.commitTransaction()
  } catch (error) {
    console.error(`Error executing queries/${filename}.sql`, error)
    await runner.rollbackTransaction()
  } finally {
    await runner.release()
  }
}

console.log('Initializing database schema...')
await bootstrap('01-schema')
console.log('Initializing database views...')
await bootstrap('02-views')
console.log('Seeding database...')
await bootstrap('03-seeds')
console.log('Running pending migrations...')
await ds.runMigrations()
