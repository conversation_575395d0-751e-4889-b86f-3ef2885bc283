import { MigrationInterface, QueryRunner } from 'typeorm'

export class ConsolidateAppModelAndDatabaseSchemas1747665298604
  implements MigrationInterface
{
  name = 'ConsolidateAppModelAndDatabaseSchemas1747665298604'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `COMMENT ON COLUMN "service_event_alert"."change_type" IS 'An enumerated value reflecting the type of change that occurred. For now, we’ll be exclusively using “ADDED” and “REMOVED”'`,
    )
    await queryRunner.query(
      `COMMENT ON COLUMN "required_form_content"."created_date" IS 'The date on which the form content was created.'`,
    )
    await queryRunner.query(
      `ALTER TABLE "required_form_content" ALTER COLUMN "created_date" SET DEFAULT now()`,
    )
    await queryRunner.query(
      `COMMENT ON COLUMN "required_form_content"."created_by_login_id" IS 'The login id of the person who created the form content.'`,
    )
    await queryRunner.query(
      `COMMENT ON COLUMN "required_form_content"."last_modified_date" IS 'The date on which the form content was last modified.'`,
    )
    await queryRunner.query(
      `ALTER TABLE "required_form_content" ALTER COLUMN "last_modified_date" SET DEFAULT now()`,
    )
    await queryRunner.query(
      `COMMENT ON COLUMN "required_form_content"."last_modified_by_login_id" IS 'The login id of the person who last modified the form content.'`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `COMMENT ON COLUMN "required_form_content"."last_modified_by_login_id" IS NULL`,
    )
    await queryRunner.query(
      `ALTER TABLE "required_form_content" ALTER COLUMN "last_modified_date" DROP DEFAULT`,
    )
    await queryRunner.query(
      `COMMENT ON COLUMN "required_form_content"."last_modified_date" IS NULL`,
    )
    await queryRunner.query(
      `COMMENT ON COLUMN "required_form_content"."created_by_login_id" IS NULL`,
    )
    await queryRunner.query(
      `ALTER TABLE "required_form_content" ALTER COLUMN "created_date" DROP DEFAULT`,
    )
    await queryRunner.query(
      `COMMENT ON COLUMN "required_form_content"."created_date" IS NULL`,
    )
    await queryRunner.query(
      `COMMENT ON COLUMN "service_event_alert"."change_type" IS 'An enumerated value reflecting the type of change that occurred. For now, we’ll be exclusively using “added” and “removed”'`,
    )
  }
}
