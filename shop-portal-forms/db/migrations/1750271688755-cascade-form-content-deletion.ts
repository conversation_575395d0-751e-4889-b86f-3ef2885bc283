import { MigrationInterface, QueryRunner } from 'typeorm'

export class CascadeFormContentDeletion1750271688755
  implements MigrationInterface
{
  name = 'CascadeFormContentDeletion1750271688755'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "required_form_content" DROP CONSTRAINT "rqrdfrm_to_rqrfrmcntnt_constr1"`,
    )
    await queryRunner.query(
      `ALTER TABLE "required_form_content" ADD CONSTRAINT "rqrdfrm_to_rqrfrmcntnt_constr1" FOREIGN KEY ("required_form_id") REFERENCES "required_form"("required_form_id") ON DELETE CASCADE ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "required_form_content" DROP CONSTRAINT "rqrdfrm_to_rqrfrmcntnt_constr1"`,
    )
    await queryRunner.query(
      `ALTER TABLE "required_form_content" ADD CONSTRAINT "rqrdfrm_to_rqrfrmcntnt_constr1" FOREIGN KEY ("required_form_id") REFERENCES "required_form"("required_form_id") ON DELETE CASCADE ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
  }
}
