import { MigrationInterface, QueryRunner } from 'typeorm'

export class InitializeInboundZoneInspectionFormGroup1747675393837
  implements MigrationInterface
{
  name = 'InitializeInboundZoneInspectionFormGroup1747675393837'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO "dci_form_group" ("dci_form_group_id", "description")
        VALUES (1, 'Inbd Visual Zone');`,
    )
    await queryRunner.query(
      `UPDATE "dci_form"
        SET "dci_form_group_id" = 1
        WHERE "dci_form_id" IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10);`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "dci_form"
        SET "dci_form_group_id" = NULL
        WHERE "dci_form_id" IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10);`,
    )

    await queryRunner.query(
      `DELETE FROM "dci_form_group"
        WHERE "dci_form_group_id" = 1;`,
    )
  }
}
