import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddIdToLocationInteriorExterior1749131133961
  implements MigrationInterface
{
  name = 'AddIdToLocationInteriorExterior1749131133961'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD "location_interior_exterior_id" SERIAL NOT NULL`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "location_interior_exterior_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "location_interior_exterior_pkey" PRIMARY KEY ("dropdown_source_id", "locn_group_id", "interior_exterior_id", "location_interior_exterior_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "locngrp_2_locnintext_constr01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "intext_2_locnintext_constr01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "location_interior_exterior_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "location_interior_exterior_pkey" PRIMARY KEY ("locn_group_id", "interior_exterior_id", "location_interior_exterior_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "location_interior_exterior_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "location_interior_exterior_pkey" PRIMARY KEY ("interior_exterior_id", "location_interior_exterior_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "location_interior_exterior_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "location_interior_exterior_pkey" PRIMARY KEY ("location_interior_exterior_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "UQ_91c8bd80aff70feec3804de60c7" UNIQUE ("dropdown_source_id", "locn_group_id", "interior_exterior_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "intext_2_locnintext_constr01" FOREIGN KEY ("interior_exterior_id") REFERENCES "interior_exterior"("interior_exterior_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "locngrp_2_locnintext_constr01" FOREIGN KEY ("locn_group_id") REFERENCES "location_group"("locn_group_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "locngrp_2_locnintext_constr01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "intext_2_locnintext_constr01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "UQ_91c8bd80aff70feec3804de60c7"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "location_interior_exterior_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "location_interior_exterior_pkey" PRIMARY KEY ("interior_exterior_id", "location_interior_exterior_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "location_interior_exterior_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "location_interior_exterior_pkey" PRIMARY KEY ("locn_group_id", "interior_exterior_id", "location_interior_exterior_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "location_interior_exterior_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "location_interior_exterior_pkey" PRIMARY KEY ("dropdown_source_id", "locn_group_id", "interior_exterior_id", "location_interior_exterior_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "intext_2_locnintext_constr01" FOREIGN KEY ("interior_exterior_id") REFERENCES "interior_exterior"("interior_exterior_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "locngrp_2_locnintext_constr01" FOREIGN KEY ("locn_group_id") REFERENCES "location_group"("locn_group_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP CONSTRAINT "location_interior_exterior_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" ADD CONSTRAINT "location_interior_exterior_pkey" PRIMARY KEY ("dropdown_source_id", "locn_group_id", "interior_exterior_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_interior_exterior" DROP COLUMN "location_interior_exterior_id"`,
    )
  }
}
