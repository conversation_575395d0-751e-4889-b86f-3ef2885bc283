import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddClientVersionToRequiredForm1749796711871
  implements MigrationInterface
{
  name = 'AddClientVersionToRequiredForm1749796711871'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "required_form" ADD "client_version" character varying(20)`,
    )
    await queryRunner.query(
      `COMMENT ON COLUMN "required_form"."client_version" IS 'The version of the client that exported this form.'`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `COMMENT ON COLUMN "required_form"."client_version" IS 'The version of the client that exported this form.'`,
    )
    await queryRunner.query(
      `ALTER TABLE "required_form" DROP COLUMN "client_version"`,
    )
  }
}
