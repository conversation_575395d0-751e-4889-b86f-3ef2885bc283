import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddIdToSecondaryTcidPatternSource1749131220844
  implements MigrationInterface
{
  name = 'AddIdToSecondaryTcidPatternSource1749131220844'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD "secondary_tcid_pattern_source_id" SERIAL NOT NULL`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "secondary_tcid_pattern_source_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "secondary_tcid_pattern_source_pkey" PRIMARY KEY ("interior_exterior_id", "locn_group_id", "secondary_tcid_pattern_source_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "intext_to_sctcidpsrc_constr01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "locngrp_to_sctcidpsrc_constr01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "secondary_tcid_pattern_source_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "secondary_tcid_pattern_source_pkey" PRIMARY KEY ("locn_group_id", "secondary_tcid_pattern_source_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "secondary_tcid_pattern_source_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "secondary_tcid_pattern_source_pkey" PRIMARY KEY ("secondary_tcid_pattern_source_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "UQ_019c043726164746af845a38892" UNIQUE ("interior_exterior_id", "locn_group_id", "tcid_component_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "intext_to_sctcidpsrc_constr01" FOREIGN KEY ("interior_exterior_id") REFERENCES "interior_exterior"("interior_exterior_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "locngrp_to_sctcidpsrc_constr01" FOREIGN KEY ("locn_group_id") REFERENCES "location_group"("locn_group_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "locngrp_to_sctcidpsrc_constr01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "intext_to_sctcidpsrc_constr01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "UQ_3284d7914de4ac56bd078461f35"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "secondary_tcid_pattern_source_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "secondary_tcid_pattern_source_pkey" PRIMARY KEY ("locn_group_id", "secondary_tcid_pattern_source_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "secondary_tcid_pattern_source_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "secondary_tcid_pattern_source_pkey" PRIMARY KEY ("interior_exterior_id", "locn_group_id", "secondary_tcid_pattern_source_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "locngrp_to_sctcidpsrc_constr01" FOREIGN KEY ("locn_group_id") REFERENCES "location_group"("locn_group_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "intext_to_sctcidpsrc_constr01" FOREIGN KEY ("interior_exterior_id") REFERENCES "interior_exterior"("interior_exterior_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP CONSTRAINT "secondary_tcid_pattern_source_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" ADD CONSTRAINT "secondary_tcid_pattern_source_pkey" PRIMARY KEY ("interior_exterior_id", "locn_group_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "secondary_tcid_pattern_source" DROP COLUMN "secondary_tcid_pattern_source_id"`,
    )
  }
}
