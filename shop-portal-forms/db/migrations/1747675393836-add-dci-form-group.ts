import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddDciFormGroup1747675393836 implements MigrationInterface {
  name = 'AddDciFormGroup1747675393836'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "dci_form_group" ("dci_form_group_id" SERIAL NOT NULL, "description" character varying(200) NOT NULL, CONSTRAINT "PK_9a36e53d13b661aa33ed7b2be53" PRIMARY KEY ("dci_form_group_id")); COMMENT ON COLUMN "dci_form_group"."dci_form_group_id" IS 'A unique key for the form group table.'; COMMENT ON COLUMN "dci_form_group"."description" IS 'A description of the form group'`,
    )
    await queryRunner.query(
      `COMMENT ON TABLE "dci_form_group" IS 'A DCI Form Group represents a set of related DCI Forms'`,
    )
    await queryRunner.query(
      `ALTER TABLE "dci_form" ADD "dci_form_group_id" integer`,
    )
    await queryRunner.query(
      `COMMENT ON COLUMN "dci_form"."dci_form_group_id" IS 'The id of the form group that the form belongs to.'`,
    )
    await queryRunner.query(
      `CREATE INDEX "dci_form_fk4" ON "dci_form" ("dci_form_group_id") `,
    )
    await queryRunner.query(
      `ALTER TABLE "dci_form" ADD CONSTRAINT "dcifrmgrp_to_dcifrm_constr1" FOREIGN KEY ("dci_form_group_id") REFERENCES "dci_form_group"("dci_form_group_id") ON DELETE SET NULL ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "dci_form" DROP CONSTRAINT "dcifrmgrp_to_dcifrm_constr1"`,
    )
    await queryRunner.query(`DROP INDEX "public"."dci_form_fk4"`)
    await queryRunner.query(
      `COMMENT ON COLUMN "dci_form"."dci_form_group_id" IS 'The id of the form group that the form belongs to.'`,
    )
    await queryRunner.query(
      `ALTER TABLE "dci_form" DROP COLUMN "dci_form_group_id"`,
    )
    await queryRunner.query(`COMMENT ON TABLE "dci_form_group" IS NULL`)
    await queryRunner.query(`DROP TABLE "dci_form_group"`)
  }
}
