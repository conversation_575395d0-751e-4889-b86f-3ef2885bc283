import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddIdToZoneLocationDropdown1749131294652
  implements MigrationInterface
{
  name = 'AddIdToZoneLocationDropdown1749131294652'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" ADD "zone_location_dropdown_id" SERIAL NOT NULL`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" DROP CONSTRAINT "zone_location_dropdown_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" ADD CONSTRAINT "zone_location_dropdown_pkey" PRIMARY KEY ("dropdown_source_id", "zone_locn_code", "zone_location_dropdown_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" DROP CONSTRAINT "zonelocn_2_zonelocndrpdwn_con1"`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" DROP CONSTRAINT "zone_location_dropdown_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" ADD CONSTRAINT "zone_location_dropdown_pkey" PRIMARY KEY ("zone_locn_code", "zone_location_dropdown_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" DROP CONSTRAINT "zone_location_dropdown_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" ADD CONSTRAINT "zone_location_dropdown_pkey" PRIMARY KEY ("zone_location_dropdown_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" ADD CONSTRAINT "UQ_fe0f1c9c9d80db58b04ddb9b840" UNIQUE ("dropdown_source_id", "zone_locn_code")`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" ADD CONSTRAINT "zonelocn_2_zonelocndrpdwn_con1" FOREIGN KEY ("zone_locn_code") REFERENCES "zone_location"("zone_locn_code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" DROP CONSTRAINT "zonelocn_2_zonelocndrpdwn_con1"`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" DROP CONSTRAINT "UQ_fe0f1c9c9d80db58b04ddb9b840"`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" DROP CONSTRAINT "zone_location_dropdown_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" ADD CONSTRAINT "zone_location_dropdown_pkey" PRIMARY KEY ("zone_locn_code", "zone_location_dropdown_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" DROP CONSTRAINT "zone_location_dropdown_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" ADD CONSTRAINT "zone_location_dropdown_pkey" PRIMARY KEY ("dropdown_source_id", "zone_locn_code", "zone_location_dropdown_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" ADD CONSTRAINT "zonelocn_2_zonelocndrpdwn_con1" FOREIGN KEY ("zone_locn_code") REFERENCES "zone_location"("zone_locn_code") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" DROP CONSTRAINT "zone_location_dropdown_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" ADD CONSTRAINT "zone_location_dropdown_pkey" PRIMARY KEY ("dropdown_source_id", "zone_locn_code")`,
    )
    await queryRunner.query(
      `ALTER TABLE "zone_location_dropdown" DROP COLUMN "zone_location_dropdown_id"`,
    )
  }
}
