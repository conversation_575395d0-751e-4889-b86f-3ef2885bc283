import { MigrationInterface, QueryRunner } from 'typeorm'

// Define minimal definition here to match what's in DB at the time the migration was created.
// If we used our model, then we'd be using the definition at the time the migration was run,
// and that may not match what's there.
type RawFormResponseContent = {
  required_form_content_id: number
  row_version_num: number
  form_responses: {
    response: object
    signatures: Record<
      string,
      {
        signedAt: string
        signedBy: {
          id: string
          name: string
        }
        type?: string
      }
    >
  }
}

export class SetDefaultSignatureType1750449553123
  implements MigrationInterface
{
  name = 'SetDefaultSignatureType1750449553123'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Migrating UP requires adding a signature type to all signatures in all forms. Since the
    // default was "complete" before a type was added, that's what we'll add.

    // Get all the form responses.
    const formResponses: RawFormResponseContent[] = (await queryRunner.query(
      `SELECT required_form_content_id, row_version_num, form_responses from "required_form_content"`,
    )) as RawFormResponseContent[]

    if (formResponses && formResponses.length > 0) {
      for (const response of formResponses) {
        const signatures = response.form_responses?.signatures
        if (signatures) {
          Object.values(signatures).forEach((signature) => {
            if (!signature.type) {
              // No type exists for this signature, so set it.
              signature.type = 'complete'
            }
          })

          await queryRunner.query(
            `UPDATE required_form_content SET form_responses = $1, row_version_num = $2 WHERE required_form_content_id = $3`,
            [
              JSON.stringify(response.form_responses),
              response.row_version_num + 1,
              response.required_form_content_id,
            ],
          )
        }
      }
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  public async down(): Promise<void> {
    // It doesn't seem worth removing the "type" from each row in the DB since it'll just be skipped over anyway.
  }
}
