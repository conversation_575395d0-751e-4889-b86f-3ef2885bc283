import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddIdToLocationCorrosionType1749130931600
  implements MigrationInterface
{
  name = 'AddIdToLocationCorrosionType1749130931600'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD "location_corrosion_type_id" SERIAL NOT NULL`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "location_corrosion_type_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "location_corrosion_type_pkey" PRIMARY KEY ("dropdown_source_id", "locn_group_id", "corrosion_type_id", "location_corrosion_type_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "locngrp_2_locncorrtype_const01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "corrtype_2_locncorrtype_cons01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "location_corrosion_type_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "location_corrosion_type_pkey" PRIMARY KEY ("locn_group_id", "corrosion_type_id", "location_corrosion_type_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "location_corrosion_type_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "location_corrosion_type_pkey" PRIMARY KEY ("corrosion_type_id", "location_corrosion_type_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "location_corrosion_type_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "location_corrosion_type_pkey" PRIMARY KEY ("location_corrosion_type_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "UQ_2b9e18cc1fa35e3b1f69ea46f31" UNIQUE ("dropdown_source_id", "locn_group_id", "corrosion_type_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "locngrp_2_locncorrtype_const01" FOREIGN KEY ("locn_group_id") REFERENCES "location_group"("locn_group_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "corrtype_2_locncorrtype_cons01" FOREIGN KEY ("corrosion_type_id") REFERENCES "corrosion_type"("corrosion_type_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "corrtype_2_locncorrtype_cons01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "locngrp_2_locncorrtype_const01"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "UQ_2b9e18cc1fa35e3b1f69ea46f31"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "location_corrosion_type_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "location_corrosion_type_pkey" PRIMARY KEY ("corrosion_type_id", "location_corrosion_type_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "location_corrosion_type_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "location_corrosion_type_pkey" PRIMARY KEY ("locn_group_id", "corrosion_type_id", "location_corrosion_type_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "location_corrosion_type_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "location_corrosion_type_pkey" PRIMARY KEY ("dropdown_source_id", "locn_group_id", "corrosion_type_id", "location_corrosion_type_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "corrtype_2_locncorrtype_cons01" FOREIGN KEY ("corrosion_type_id") REFERENCES "corrosion_type"("corrosion_type_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "locngrp_2_locncorrtype_const01" FOREIGN KEY ("locn_group_id") REFERENCES "location_group"("locn_group_id") ON DELETE NO ACTION ON UPDATE NO ACTION DEFERRABLE INITIALLY DEFERRED`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP CONSTRAINT "location_corrosion_type_pkey"`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" ADD CONSTRAINT "location_corrosion_type_pkey" PRIMARY KEY ("dropdown_source_id", "locn_group_id", "corrosion_type_id")`,
    )
    await queryRunner.query(
      `ALTER TABLE "location_corrosion_type" DROP COLUMN "location_corrosion_type_id"`,
    )
  }
}
