import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddInbdExteriorCoatingInspTemplate1751305697138
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO form_template (
            form_template_id,
            created_date,
            created_by_login_id,
            last_modified_date,
            last_modified_login_id,
            template_desc) 
        SELECT 
            COALESCE(MAX(form_template_id), 0) + 1,
            NOW(),
            'system',
            NOW(),
            'system',
            'Inbd Exterior Coating Insp'
        FROM form_template;
    `)

    await queryRunner.query(`
        UPDATE dci_form
        SET form_template_id = (
            SELECT form_template_id FROM form_template
            WHERE template_desc = 'Inbd Exterior Coating Insp'
        )
        WHERE dci_form_id = 48;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        UPDATE dci_form
        SET form_template_id = (
            SELECT form_template_id FROM form_template
            WHERE template_desc = 'Inbound Inspection'
        )
        WHERE dci_form_id = 48;
    `)

    await queryRunner.query(`
        DELETE FROM form_template
        WHERE template_desc = 'Inbd Exterior Coating Insp';
    `)
  }
}
