import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddNotNullConstraintsToNotTestedReason1749128084324
  implements MigrationInterface
{
  name = 'AddNotNullConstraintsToNotTestedReason1749128084324'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "not_tested_reason" ALTER COLUMN "not_tested_reason_code" SET NOT NULL`,
    )
    await queryRunner.query(
      `ALTER TABLE "not_tested_reason" ALTER COLUMN "not_tested_reason_desc" SET NOT NULL`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "not_tested_reason" ALTER COLUMN "not_tested_reason_desc" DROP NOT NULL`,
    )
    await queryRunner.query(
      `ALTER TABLE "not_tested_reason" ALTER COLUMN "not_tested_reason_code" DROP NOT NULL`,
    )
  }
}
