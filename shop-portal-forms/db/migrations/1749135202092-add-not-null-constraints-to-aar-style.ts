import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddNotNullConstraintsToAarStyle1749135202092
  implements MigrationInterface
{
  name = 'AddNotNullConstraintsToAarStyle1749135202092'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "aar_style" ALTER COLUMN "aar_style_code" SET NOT NULL`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "aar_style" ALTER COLUMN "aar_style_code" DROP NOT NULL`,
    )
  }
}
