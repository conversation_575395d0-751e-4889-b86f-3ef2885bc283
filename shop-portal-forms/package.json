{"name": "gatx-platform-one-shop-portal-forms", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start:dev": "bun --hot run ./src/main.ts", "start:debug": "bun --inspect run ./src/main.ts", "start:prod": "bun run ./src/main.ts", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test:watch": "bun test --watch", "test:cov": "bun test --coverage", "test:debug": "bun test --inspect-brk", "test:e2e": "bun test --config ./test/jest-e2e.json", "db:migration:create": "cd db/migrations && typeorm migration:create", "db:migration:up": "typeorm -d db/datasource.ts migration:run", "db:migration:down": "typeorm -d db/datasource.ts migration:revert", "db:migration:generate": "typeorm -d db/datasource.ts migration:generate", "db:bootstrap": "bun run db/bootstrap/index.ts", "db:drop": "typeorm -d db/datasource.ts schema:drop", "db:reset": "bun run db:drop && bun run db:bootstrap", "db:seed:orchestration": "bun run db/orchestration.seed.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.4.4", "@nestjs/platform-express": "^10.4.5", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^10.0.2", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "pg": "^8.13.0", "pg-connection-string": "^2.7.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "uuid": "^10.0.0", "xmlbuilder2": "^3.1.1", "zod": "^3.23.8"}, "devDependencies": {"@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bun": "^1.1.11", "@types/express": "^4.17.17", "@types/jest": "^29.5.13", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}