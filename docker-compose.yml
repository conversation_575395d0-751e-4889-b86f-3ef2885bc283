services:
  landing-page:
    build:
      context: ./landing-page
      target: base
      secrets:
        - github_token
    command: sh -c "yarn install && yarn dev"
    ports:
      - 2999:3000
    depends_on:
      - authorization
    env_file:
      - .env.authentication
    environment:
      AUTHORIZATIONS_SERVICE_API_ROOT: http://authorization:3000
      GITHUB_TOKEN: '${GITHUB_TOKEN}'
    volumes:
      - ./landing-page:/app
      - landing-page-node_cache:/app/node_modules

  authorization:
    build:
      context: ./authorization
      target: development
    command: sh -c "bun install && bun start:dev"
    ports:
      - 2998:3000
    depends_on:
      - db-auth
    environment:
      DB_URL: *******************************************/common_authorization_dev
    volumes:
      - ./authorization:/usr/src/app
      - authorization-node_cache:/usr/src/app/node_modules

  shop-portal:
    build:
      context: ./shop-portal-ui
      target: base
      secrets:
        - github_token
    command: sh -c "yarn install && yarn dev"
    ports:
      - 3000:3000
    depends_on:
      - landing-page
      - railcars
      - shop-events
      - shop-portal-forms
      - redis
    env_file:
      - .env.authentication
    environment:
      SERVICE_API_ROOT_RAILCARS: http://railcars:3000
      SERVICE_API_ROOT_SHOP_EVENTS: http://shop-events:3000
      SERVICE_API_ROOT_FORMS: http://shop-portal-forms:3000
      GATX_SERVICE_API_ROOT_INTERCHANGEABLE_PARTS: http://on-prem-substitute:3000/asset/interchangeableParts
      GATX_SERVICE_API_ROOT_DOCUMENTS: http://on-prem-substitute:3000/documents
      GATX_SERVICE_API_ROOT_PROGRAM_DEFINITIONS: http://on-prem-substitute:3000/serviceEvent

      REDIS_URL: redis://redis:6379
      GITHUB_TOKEN: '${GITHUB_TOKEN}'
      NEXT_PUBLIC_EMAIL_SHOP_PORTAL_SUPPORT: <EMAIL>
    volumes:
      - ./shop-portal-ui:/app
      - shop-portal-node_cache:/app/node_modules

  railcars:
    build:
      context: ./railcars
      target: development
    command: sh -c "bun install && bun start:dev"
    ports:
      - 3001:3000
    depends_on:
      - on-prem-substitute
    environment:
      GATX_SERVICE_API_ROOT_ASSET: http://on-prem-substitute:3000/asset
      GATX_SERVICE_API_ROOT_SERVICE_EVENT: http://on-prem-substitute:3000/serviceEvent
      REDIS_URL: redis://redis:6379
    volumes:
      - ./railcars:/usr/src/app
      - railcars-node_cache:/usr/src/app/node_modules

  shop-events:
    build:
      context: ./shop-events
      target: development
    command: sh -c "bun install && bun start:dev"
    ports:
      - 3002:3000
    depends_on:
      - on-prem-substitute
    environment:
      GATX_SERVICE_API_ROOT_SERVICE_EVENT: http://on-prem-substitute:3000/serviceEvent
    volumes:
      - ./shop-events:/usr/src/app
      - shop-events-node_cache:/usr/src/app/node_modules

  shop-portal-forms:
    build:
      context: ./shop-portal-forms
      target: development
    command: sh -c "bun install && bun start:dev"
    ports:
      - 3003:3000
    depends_on:
      - db-forms
    environment:
      DB_URL: ********************************************/shop_portal_forms_dev
      PORT: 3000
    volumes:
      - ./shop-portal-forms:/usr/src/app
      - shop-portal-forms-node_cache:/usr/src/app/node_modules

  db-auth:
    image: postgres:16.4
    ports:
      - 15431:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: common_authorization_dev
    volumes:
      - db-auth_data:/var/lib/postgresql/data

  db-forms:
    image: postgres:16.4
    ports:
      - 15432:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: shop_portal_forms_dev
    volumes:
      - db-forms_data:/var/lib/postgresql/data

  redis:
    image: redis/redis-stack:latest
    ports:
      - 6379:6379

  on-prem-substitute:
    build:
      context: ./on-prem-substitute
      target: development
    command: sh -c "bun install && bun start:dev"
    ports:
      - 3030:3000
    volumes:
      - ./on-prem-substitute:/usr/src/app
      - on-prem-substitute-node_cache:/usr/src/app/node_modules

secrets:
  github_token:
    environment: GITHUB_TOKEN

volumes:
  landing-page-node_cache: {}
  shop-portal-node_cache: {}
  authorization-node_cache: {}
  railcars-node_cache: {}
  shop-events-node_cache: {}
  shop-portal-forms-node_cache: {}
  db-forms_data: {}
  db-auth_data: {}
  on-prem-substitute-node_cache: {}
