import { AnchorHTMLAttributes } from 'react'
import { <PERSON> } from 'react-router-dom'

/**
 * RouterLink is a `react-dom-router`-specific implementation of a link
 * component to be used anywhere that expects to work with a conventional anchor
 * tag. It translates the `href` prop into a `to` prop for the underlying router
 * `Link`.
 */
export const RouterLink = ({
  href,
  children,
  ...props
}: AnchorHTMLAttributes<HTMLAnchorElement>) => {
  return (
    <Link to={href ?? ''} {...props}>
      {children}
    </Link>
  )
}
