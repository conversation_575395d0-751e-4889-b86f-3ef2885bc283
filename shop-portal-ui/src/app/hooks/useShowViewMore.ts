import { debounce } from '@/utils/debounce'
import { useEffect, useId, useRef, useState } from 'react'

const MAX_STATUS_TAB_HEIGHT = 77

function updateListItemAccessibility(container: HTMLElement | null): void {
  if (!container) return

  const items = Array.from(container.getElementsByTagName('li'))

  requestAnimationFrame(() => {
    const listRect = container.getBoundingClientRect()

    for (const item of items) {
      const itemRect = item.getBoundingClientRect()
      const isHidden =
        itemRect.bottom > listRect.bottom || itemRect.top < listRect.top

      if (item instanceof HTMLLIElement) {
        const anchor = item.querySelector('a')

        if (isHidden) {
          item.setAttribute('aria-hidden', 'true')
          if (anchor) {
            anchor.setAttribute('tabindex', '-1')
          }
        } else {
          item.removeAttribute('aria-hidden')
          if (anchor) {
            anchor.setAttribute('tabindex', '0')
          }
        }
      }
    }
  })
}

function isTargetWithinThreshold(target: HTMLElement) {
  return Math.floor(target.scrollHeight) <= MAX_STATUS_TAB_HEIGHT
}

function expand(target: HTMLElement) {
  target.style.maxHeight = 'none'
  target.style.overflow = 'visible'
}

function collapse(target: HTMLElement) {
  target.style.maxHeight = `${MAX_STATUS_TAB_HEIGHT}px`
  target.style.overflow = 'hidden'
}

export function useShowViewMore(): {
  isExpanded: boolean
  state: 'expanded' | 'collapsed'
  refs: {
    targetRef: (el: HTMLDivElement) => void
    handleRef: (el: HTMLButtonElement) => void
  }
} {
  const targetRef = useRef<HTMLDivElement>(null)
  const handleRef = useRef<HTMLButtonElement>(null)

  const targetId = useId()
  const handleId = useId()

  const [expanded, setExpanded] = useState(false)

  if (targetRef.current) {
    if (expanded) {
      expand(targetRef.current)
    } else {
      collapse(targetRef.current)
    }
  }

  useEffect(() => {
    const handleResize = debounce(() => {
      if (handleRef.current && targetRef.current) {
        const target = targetRef.current
        const handle = handleRef.current

        handle.hidden = isTargetWithinThreshold(target)
      }

      updateListItemAccessibility(targetRef.current)
    }, 50)

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return {
    isExpanded: expanded,
    state: expanded ? 'expanded' : 'collapsed',
    refs: {
      /**
       * Reference function to be attached to the target element. Executes on every update.
       */
      targetRef: (el: HTMLDivElement) => {
        /**
         * Initialization code to execute when the target is first rendered.
         */
        if (el && !targetRef.current) {
          el.id = targetId

          /**
           * The target starts collapsed to avoid layout shift when
           * there is not enough space during the initial render.
           */
          collapse(el)

          const handle = document.getElementById(handleId)

          /**
           * If the handle is rendered first, the target should be used
           * to determine if it should be hidden.
           */
          if (handle) {
            handle.hidden = isTargetWithinThreshold(el)
          }

          targetRef.current = el
        }
      },
      /**
       * Reference function to be attached to the handle element. Executes on every update.
       */
      handleRef: (el: HTMLButtonElement) => {
        /**
         * Initialization code to execute when the handle is first rendered.
         */
        if (el && !handleRef.current) {
          el.id = handleId
          el.setAttribute('aria-controls', targetId)
          el.onclick = () => {
            setExpanded((prev) => !prev)
            updateListItemAccessibility(targetRef.current)
          }

          const target = document.getElementById(targetId)

          /**
           * If the target is rendered first, it should be used to
           * determine if the handle should be hidden.
           *
           * Otherwise, the handle starts hidden.
           */
          if (target) {
            el.hidden = isTargetWithinThreshold(target)
          } else {
            el.hidden = true
          }

          handleRef.current = el
        }
      },
    },
  }
}
