import ScrollTopButton from '@/app/components/ScrollTopButton/locales/en.json'
import SidePanelLayout from '@/app/components/SidePanelLayout/locales/en.json'
import Header from '@/app/pages/Header/locales/en.json'
import MainTabs from '@/app/pages/MainTabs/locales/en.json'
import Search from '@/app/pages/Search/locales/en.json'
import Comments from '@/features/comments/locales/en.json'
import Connectivity from '@/features/connectivity/locales/en.json'
import Documents from '@/features/documents/locales/en.json'
import Forms from '@/features/forms/locales/en.json'
import FormUtils from '@/features/forms/utils/locales/en.json'
import InterchangeableParts from '@/features/interchangeable-parts/locales/en.json'
import Railcars from '@/features/railcars/locales/en.json'
import ServiceEvents from '@/features/service-events/locales/en.json'
import CommonUI from '../locales/en.json'

const en = {
  Comments,
  CommonUI,
  Connectivity,
  Documents,
  Forms,
  FormUtils,
  Header,
  InterchangeableParts,
  MainTabs,
  Railcars,
  ScrollTopButton,
  Search,
  ServiceEvents,
  SidePanelLayout,
} as const

export { en as enResources }
