import { getClient } from '@/utils/axios'
import { getEnvironment } from '@/utils/Environment'

type TokenResponse = {
  token_type: 'Bearer'
  expires_in: number
  ext_expires_in: number
  access_token: string
}

let CACHED_TOKEN: { data: TokenResponse; expires: number } | undefined

/**
 * `withServiceToken` is a utility function that retrieves a service-specific
 * authorization token from a configured issuer and passes it to the provided
 * callback as an Authorization header.
 *
 * The token is cached for the duration of its validity to avoid unnecessary
 * requests to the issuer. There's a possibility of race conditions here
 * resulting in multiple token fetches, but the tokens are valid for an hour,
 * so the impact should be minimal.
 *
 * This token is a necessary part of the workflow to invoke MuleSoft services.
 */
export const withServiceToken = async <T>(
  callback: ({ Authorization }: Record<string, string>) => T,
): Promise<T> => {
  const env = getEnvironment()
  if (!env.GATX_SERVICE_TOKEN_ISSUER_URL) {
    return callback({ Authorization: '' })
  }

  let response: TokenResponse | undefined

  if (CACHED_TOKEN && CACHED_TOKEN.expires > new Date().getTime()) {
    response = CACHED_TOKEN.data
  } else {
    const { data } = await getClient('serviceToken').post<TokenResponse>(
      '/token',
      {
        grant_type: 'client_credentials',
        scope: 'https://graph.microsoft.com/.default',
        client_id: env.GATX_SERVICE_TOKEN_ISSUER_CLIENT_ID,
        client_secret: env.GATX_SERVICE_TOKEN_ISSUER_CLIENT_SECRET,
      },
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    )

    response = data
    CACHED_TOKEN = {
      data: response,
      expires: new Date().getTime() + response.expires_in * 1000,
    }
  }

  return callback({
    Authorization: `${response.token_type} ${response.access_token}`,
  })
}
