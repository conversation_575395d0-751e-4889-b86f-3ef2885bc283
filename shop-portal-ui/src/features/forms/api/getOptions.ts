import { performStandaloneCachingQuery } from '@/features/caching/utils'
import { DEFAULT_TTL_SECONDS } from '@/features/caching/utils/ttl'
import { getListOptions } from '../api/getListOptions'

async function getOptionsServer(
  listId: string,
  query?: Record<string, unknown>,
) {
  const result = await getListOptions(listId, query ?? {})
  if (result.success) {
    return result.response
  }

  return []
}

async function getOptionsClient(
  listId: string,
  query?: Record<string, unknown>,
) {
  const q = performStandaloneCachingQuery()
  const queryId = encodeURIComponent(JSON.stringify({ listId, query }))

  // TODO: review updating the Caching layer
  // to support having the QueryParms as an object and not a single string
  const doQuery = () => {
    return q(queryId, {
      dataSetName: 'listOptions',
      remoteQueryFunction: () => getListOptions(listId, query ?? {}),
      timeToLive: DEFAULT_TTL_SECONDS,
    })
  }

  const { data: opts = [] } = await doQuery()
  return opts
}

const getOptions = async (listId: string, query?: Record<string, unknown>) => {
  const getOpts = () =>
    typeof window === 'undefined'
      ? getOptionsServer(listId, query)
      : getOptionsClient(listId, query)

  const opts = await getOpts()
  return opts.map((o) => ({
    label: o.label,
    value: o.label,
    metadata: o,
  }))
}

export { getOptions }
