import { saveFormContent } from '../api'
import { useForm } from '../hooks/useForm'
import { useFormDraft } from '../hooks/useFormDraft'
import { FormContent } from '../types/Form'
import useFormAction from './useFormAction'

export const useFormSave = (formId: string) => {
  const [currentContent] = useFormDraft(formId)
  const { data: form } = useForm({ formId })

  console.log('currentContent', currentContent)

  return useFormAction(
    formId,
    form?.version ?? 0,
    async function save(content?: FormContent) {
      if (!form) {
        throw Error('Cannot save form: form not available')
      }

      return saveFormContent(form, {
        content: content ?? currentContent,
        version: form.version,
      })
    },
  )
}
