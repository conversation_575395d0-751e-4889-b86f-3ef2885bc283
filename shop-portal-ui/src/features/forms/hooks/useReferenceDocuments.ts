import { useProcedureDocuments } from '@/features/documents/hooks/useProcedureDocuments'
import { useAppContext } from '@/providers/AppProvider'

export default function useReferenceDocuments(formId: string) {
  const { serviceEventId } = useAppContext()
  const { data: procedures = [] } = useProcedureDocuments({
    serviceEventId,
  })

  return (
    procedures.find((p) => p.requiredFormId === formId)?.referenceDocuments ??
    []
  )
}
