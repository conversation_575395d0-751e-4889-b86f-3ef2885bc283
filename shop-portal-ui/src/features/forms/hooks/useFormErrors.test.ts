import { _setupCachingForTests } from '@/features/caching/providers'
import {
  _ClientDBRxDatabase,
  _setupClientDBForTests,
  _setupRxDB,
} from '@/features/client-db/providers'
import * as formQuestionHooks from '@/features/forms/providers/FormQuestionProvider'
import { resolveSchema } from '@/features/forms/utils/schema'
import { renderHook } from '@/tests/react'
import { Json } from '@/types/Json'
import { ZodSchema } from 'zod'
import { CleaningGeneralResponse } from '../patterns/cleaning-confirmation/types/CleaningGeneral'

import { useFormErrors } from './useFormErrors'

jest.mock('@/features/forms/api/getListOptions', () => ({
  getListOptions: jest.fn().mockResolvedValue({
    success: true,
    response: [
      { id: 1, value: 'Nitrogen', label: 'Nitrogen', metadata: {}, list: '' },
    ],
  }),
}))

jest.mock('@/features/forms/providers/FormQuestionProvider', () => ({
  useFormQuestionResponse: jest.fn(),
  usePatternSchema: jest.fn(),
}))

async function mockPatternSchema(Schema: ZodSchema) {
  _setupClientDBForTests(await _setupRxDB(true))
  _setupCachingForTests()
  await resolveSchema(Schema)
  jest.spyOn(formQuestionHooks, 'usePatternSchema').mockReturnValue(Schema)
}

function mockQuestionResponse(response: Record<string, Json | undefined>) {
  jest
    .spyOn(formQuestionHooks, 'useFormQuestionResponse')
    .mockReturnValue([response, jest.fn()])
}

function useHook(...args: Parameters<typeof useFormErrors>) {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  return renderHook(() => useFormErrors(...args)).result.current
}

describe('useFormErrors on CleaningGeneralResponse', () => {
  beforeAll(async () => {
    await mockPatternSchema(CleaningGeneralResponse)
  })

  afterAll(async () => {
    await _ClientDBRxDatabase?.destroy()
    jest.clearAllMocks()
  })

  describe('cleaningInspection with "inches" measurement', () => {
    it('returns empty object if the section is valid', () => {
      mockQuestionResponse({
        cleaningInspection: {
          measurement: 'inches',
          depthOfCommodity: 1.4,
          totalGallons: 100,
          exteriorCleanRequired: 'Yes',
          ventOnly: 'No',
          pH: 7,
          solidsPresent: 'Yes',
        } as Record<string, Json>,
      })

      const errors = useHook({
        path: ['cleaningInspection'],
      })

      expect(errors).toStrictEqual({})
    })

    it('returns errors for missing required fields', () => {
      mockQuestionResponse({
        cleaningInspection: {
          measurement: 'inches',
        } as Record<string, Json>,
      })

      const errors = useHook({
        path: ['cleaningInspection'],
      })

      expect(errors).toStrictEqual({
        depthOfCommodity: 'Required',
        totalGallons: 'Required',
        exteriorCleanRequired: 'Required',
        ventOnly: 'Required',
        solidsPresent: 'Required',
      })
    })
  })

  describe.each(['film', 'N/A'])(
    'cleaningInspection with "%s" measurement',
    (measurement) => {
      it('returns empty object if the section is valid', () => {
        mockQuestionResponse({
          cleaningInspection: {
            measurement,
            exteriorCleanRequired: 'Yes',
            ventOnly: 'No',
            pH: 7,
            solidsPresent: 'Yes',
          } as Record<string, Json>,
        })

        const errors = useHook({
          path: ['cleaningInspection'],
        })

        expect(errors).toStrictEqual({})
      })

      it('returns errors for missing required fields', () => {
        mockQuestionResponse({
          cleaningInspection: {
            measurement,
          } as Record<string, Json>,
        })

        const errors = useHook({
          path: ['cleaningInspection'],
        })

        expect(errors).toStrictEqual({
          exteriorCleanRequired: 'Required',
          ventOnly: 'Required',
          solidsPresent: 'Required',
        })
      })
    },
  )

  describe('cleaningApproval', () => {
    it('returns errors for missing required fields', () => {
      mockQuestionResponse({
        cleaningApproval: {},
      })

      const errors = useHook({
        path: ['cleaningApproval'],
      })

      expect(errors).toStrictEqual({
        arrivedClean: 'Required',
        confirmed: 'Required',
        approvalDetails: 'Required',
        rejectionDetails: 'Required',
      })
    })

    it.each(['Yes', 'No'])(
      'returns errors for missing required fields when arrivedClean is "%s"',
      (arrivedClean) => {
        mockQuestionResponse({
          cleaningApproval: {
            arrivedClean,
          },
        })

        const errors = useHook({
          path: ['cleaningApproval'],
        })

        expect(errors).toStrictEqual({
          confirmed: 'Required',
          approvalDetails: 'Required',
          rejectionDetails: 'Required',
        })
      },
    )
  })

  describe.each(['Yes', 'No'])(
    'cleaningApproval when arrivedClean is "%s" and confirmed is "approved"',
    (arrivedClean) => {
      it('returns empty object if the section is valid', () => {
        mockQuestionResponse({
          cleaningApproval: {
            arrivedClean,
            confirmed: 'approved',
            approvalDetails: 'Per EGP-05 Guideline',
          },
        })

        const errors = useHook({
          path: ['cleaningApproval'],
        })

        expect(errors).toStrictEqual({})
      })

      it('returns errors for missing required fields', () => {
        mockQuestionResponse({
          cleaningApproval: {
            arrivedClean,
            confirmed: 'approved',
          },
        })

        const errors = useHook({
          path: ['cleaningApproval'],
        })

        expect(errors).toStrictEqual({
          approvalDetails: 'Required',
        })
      })
    },
  )

  describe.each(['Yes', 'No'])(
    'cleaningApproval when arrivedClean is "%s" and confirmed is "rejected"',
    (arrivedClean) => {
      it('returns empty object if the section is valid', () => {
        mockQuestionResponse({
          cleaningApproval: {
            arrivedClean,
            confirmed: 'rejected',
            rejectionDetails: 'Handle DNO/DNE',
          },
        })

        const errors = useHook({
          path: ['cleaningApproval'],
        })

        expect(errors).toStrictEqual({})
      })

      it('returns errors for missing required fields', () => {
        mockQuestionResponse({
          cleaningApproval: {
            arrivedClean,
            confirmed: 'rejected',
          },
        })

        const errors = useHook({
          path: ['cleaningApproval'],
        })

        expect(errors).toStrictEqual({
          rejectionDetails: 'Required',
        })
      })
    },
  )

  describe('cleaningInformation when measurement is "inches"', () => {
    it('returns empty object if the section is valid', () => {
      mockQuestionResponse({
        cleaningInspection: {
          measurement: 'inches',
        },
        cleaningInformation: {
          cleanedPer: 'gatx_recipe',
          cleaningMethods: ['Nitrogen'],
          totalGalRemoved: 100,
          totalFlareHours: 10,
          cleaningPerformed: ['Car is Clean - Vent Only'],
          cleaningSpecType: 'reviewed',
          drumLocation: 'Drum 1',
        },
      })

      const errors = useHook({
        path: ['cleaningInformation'],
      })

      expect(errors).toStrictEqual({})
    })

    it('returns errors for missing required fields', () => {
      mockQuestionResponse({
        cleaningInspection: {
          measurement: 'inches',
        },
        cleaningInformation: {},
      })

      const errors = useHook({
        path: ['cleaningInformation'],
      })

      expect(errors).toStrictEqual({
        cleanedPer: 'Required',
        totalGalRemoved: 'Required',
        cleaningSpecType: 'Required',
      })
    })
  })

  describe.each(['film', 'N/A'])(
    'cleaningInformation with "%s" measurement',
    (measurement) => {
      it('returns empty object if the section is valid', () => {
        mockQuestionResponse({
          cleaningInspection: {
            measurement,
          },
          cleaningInformation: {
            cleanedPer: 'gatx_recipe',
            cleaningMethods: ['Nitrogen'],
            totalGalRemoved: 100,
            totalFlareHours: 10,
            cleaningPerformed: ['Car is Clean - Vent Only'],
            cleaningSpecType: 'reviewed',
            drumLocation: 'Drum 1',
          },
        })

        const errors = useHook({
          path: ['cleaningInformation'],
        })

        expect(errors).toStrictEqual({})
      })

      it('returns errors for missing required fields', () => {
        mockQuestionResponse({
          cleaningInspection: {
            measurement,
          },
          cleaningInformation: {},
        })

        const errors = useHook({
          path: ['cleaningInformation'],
        })

        expect(errors).toStrictEqual({
          cleanedPer: 'Required',
          cleaningSpecType: 'Required',
        })
      })
    },
  )

  describe('cleaningQA', () => {
    it('returns empty object if the section is valid', () => {
      mockQuestionResponse({
        cleaningQA: {
          interiorInspectionPass: 'Yes',
          exteriorInspectionPass: 'No',
          placardsRemoved: 'Yes',
          commodityStencilCovered: 'N/A',
          orangeTagApplied: 'Yes',
          NiApplied: 'Yes',
        },
      })

      const errors = useHook({
        path: ['cleaningQA'],
      })

      expect(errors).toStrictEqual({})
    })

    it('returns errors for missing required fields', () => {
      mockQuestionResponse({
        cleaningQA: {},
      })

      const errors = useHook({
        path: ['cleaningQA'],
      })

      expect(errors).toStrictEqual({
        interiorInspectionPass: 'Required',
        exteriorInspectionPass: 'Required',
        placardsRemoved: 'Required',
        commodityStencilCovered: 'Required',
        orangeTagApplied: 'Required',
        NiApplied: 'Required',
      })
    })
  })
})
