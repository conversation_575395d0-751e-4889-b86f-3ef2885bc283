import {
  FormResponsePattern,
  RESPONSE_SCHEMA_BY_PATTERN,
} from '@/features/forms/types/FormResponsePattern'
import { Json } from '@/types/Json'
import { z, ZodSchema } from 'zod'
import { getCleaningConfirmationSignables } from '../templates/CleaningConfirmation/types/CleaningConfirmation'
import { getInbdExteriorCoatingInspSignables } from '../templates/InbdExteriorCoatingInsp/types/InbdExteriorCoatingInsp'
import { getInboundInspectionSignables } from '../templates/InboundInspection/types/InboundInspection'
import { cloneSchema, ObjectSchema, resolveSchema } from '../utils/schema'
import branch from './FormBranch'

enum FormTemplate {
  INBOUND_INSPECTION = 'Inbound Inspection',
  INTERIOR_LINING = 'Interior Lining',
  BENCHMARK_THICKNESS = 'Benchmark Thickness',
  DEFECT_THICKNESS = 'Defect Thickness',
  AFTER_REPAIR_THICKNESS = 'After-Repair Thickness',
  CERTIFICATE_OF_TEST = 'Certificate Of Test',
  R1 = 'R-1',
  INITIAL_SERVICE_EQUIPMENT = 'Initial Service Equipment',
  NITROGEN_PAD_APPLICATION = 'Nitrogen Pad Application',
  FINAL_SERVICE_EQUIPMENT = 'Final Service Equipment',
  CLEANING_CONFIRMATION = 'Cleaning Confirmation',
  STRUCTURAL_INTEGRITY = 'Structural Integrity',
  NDT_IR = 'NDT-IR',
  COATING_APPLICATION = 'Coating Application',
  COIL_TEST = 'Coil Test',
  INTERIOR_SPRAY_COATING_RUBBER_LINING_REPAIR_NEW_RUBBER_LINING = 'Interior Spray Coating_Rubber Lining Repair_New Rubber Lining',
  INBD_INTERIOR_LINING_COATING_INSP = 'Inbd Interior Lining/Coating Insp',
  INBD_INTERIOR_LINING_COATING_INSP2 = 'Inbd Interior Lining_Coating Insp',
  CS_LINING_COATING = 'CS Lining Coating',
  COT_PREPOP = 'COT Prepop',
  R1_PREPOP = 'R-1 Prepop',
  INBD_EXT_COATING_INSP = 'Inbd Exterior Coating Insp',
}

enum FormStatusCode {
  NOT_STARTED = 'NW',
  IN_PROCESS = 'IP',
  SIGNED = 'CM',
  DEACTIVATED = 'IN',
  ARCHIVED = 'AR',
}

type FormQuestionGrouping = {
  id: number
  code: string | null
  name: string | null
}

type ZoneLocation = {
  code: string
  name: string
  type: 'Interior' | 'Exterior'
}

type RelatedFormItem = {
  id: string
  name: string
  status: FormStatusCode
  sequence: number
}

type FormResponseOption = {
  id: number
  name: string
  pattern: FormResponsePattern
  isDefault: boolean
}

type MRAttribute = {
  id: string
  zoneLocationCode: string
  mrLocationCode: string[]
}

type FormQuestion = {
  id: number
  dciId: number
  zone: ZoneLocation | null
  question: string
  detail: string | null
  type: string | null
  inspMethod: string | null
  cutoutZone: string | null
  responseOptions: FormResponseOption[]
  groupId: string | null
  sectionId: string | null
  mrAttributes: MRAttribute[]
}

type Form = {
  id: string
  namePartOne: string
  namePartTwo: string | null
  shortName: string
  template: FormTemplate
  statusCode: FormStatusCode
  num: string
  sections: FormQuestionGrouping[]
  groups: FormQuestionGrouping[]
  questions: FormQuestion[]
  deactivatedReasonCode: string | null
  reasonForArch: string | null
  version: number
  clientVersion: string | null
  htmlExport: string | null
  relatedForms: RelatedFormItem[]
  sequence: number
}

const FormResponseDataObject = z.record(Json.optional())
const FormResponseDataArray = FormResponseDataObject.array()

const FormResponseData = z.union([
  FormResponseDataObject,
  FormResponseDataArray,
])

const FormQuestionResponse = z.object({
  questionId: z.number(),
  responseId: z.number(),
  data: FormResponseData,
})

type FormResponseDataObject = z.infer<typeof FormResponseDataObject>
type FormResponseData = z.infer<typeof FormResponseData>
type FormQuestionResponse = z.infer<typeof FormQuestionResponse>

export const FORM_CONTENT_EMPTY_DATA = {
  response: {},
  signatures: {},
  signatureHistory: {},
  version: 0,
  createdDate: null,
  lastModifiedDate: null,
}

const getFormQuestionResponseSchemas = async (form: Form) => {
  const result: Record<number, Record<number, ZodSchema>> = {}

  for (const question of form.questions) {
    result[question.id] = {}

    for (const option of question.responseOptions) {
      const Schema = cloneSchema(
        RESPONSE_SCHEMA_BY_PATTERN[option.pattern] ?? z.never(),
      )

      await resolveSchema(Schema, {
        questionId: question.id,
        responseId: option.id,
      })

      result[question.id][option.id] = Schema
    }
  }

  return result
}

async function getFormResponseSchema(form: Form) {
  const schemas = await getFormQuestionResponseSchemas(form)

  return z.object(
    Object.fromEntries(
      form.questions.map((q) => [
        q.id,
        branch(
          ['responseId'],
          ...(q.responseOptions.map((o) =>
            z.object({
              questionId: z.literal(q.id),
              responseId: z.literal(o.id),
              data: schemas[q.id][o.id],
            }),
          ) as unknown as [ObjectSchema, ObjectSchema, ...ObjectSchema[]]),
        ),
      ]),
    ),
  )
}

const FormSignatureType = z.enum(['complete', 'deferred'])
const FormSignatureHistoryEntryType = z.union([
  FormSignatureType,
  z.enum(['unsign']),
])

const FormSignature = z.object({
  signedBy: z.object({ id: z.string(), name: z.string() }),
  signedAt: z.string().datetime(),
  extractionKey: z.string().default('signedBy'),
  extractionKeyDateLabel: z.string().default('signedAt'),
  type: FormSignatureType,
})

const FormSignatureHistoryEntry = z.record(z.any()).and(
  z.object({
    action: FormSignatureHistoryEntryType,
  }),
)

const FormSignatureHistory = z.record(
  FormSignatureHistoryEntry.array().default([]),
)

type SignatureMetadata = {
  extractionKey: string
  extractionKeyDateLabel?: string
}

const EXTRACTION_KEYS_BY_SIGNABLE: Record<string, SignatureMetadata> = {
  cleaningApproval: {
    extractionKey: 'approvedBy',
    extractionKeyDateLabel: 'approvedDate',
  },
  cleaningInformation: {
    extractionKey: 'performedBy',
    extractionKeyDateLabel: 'cleaningPerformedDate',
  },
  cleaningQA: {
    extractionKey: 'verifiedBy',
    extractionKeyDateLabel: 'verificationDate',
  },
}

function createFormSignature(
  signableId: string,
  signedBy: { id: string; name: string },
  type: FormSignatureType,
): FormSignature {
  const signedAt = new Date().toISOString()

  return FormSignature.parse({
    signedBy,
    signedAt,
    type,
    ...EXTRACTION_KEYS_BY_SIGNABLE[signableId],
  })
}

function createFormSignatureHistoryEntry(
  signableId: string,
  signedBy: { id: string; name: string },
  action: FormSignatureHistoryEntryType,
): FormSignatureHistoryEntry {
  const signedAt = new Date().toISOString()
  const extractionMetadata = EXTRACTION_KEYS_BY_SIGNABLE[signableId] ?? {}

  const userInfo = {
    id: signedBy.id,
    name: signedBy.name,
  }

  return FormSignatureHistoryEntry.parse({
    [extractionMetadata.extractionKey ?? 'signedBy']: userInfo,
    [extractionMetadata.extractionKeyDateLabel ?? 'signedAt']: signedAt,
    action,
  })
}

type GetSignables = (
  form: Form,
  response: Partial<Record<string, FormQuestionResponse>>,
) => string[]

const SIGNABLES_GETTER_BY_TEMPLATE: Partial<
  Record<FormTemplate, GetSignables>
> = {
  [FormTemplate.CLEANING_CONFIRMATION]: getCleaningConfirmationSignables,
  [FormTemplate.INBOUND_INSPECTION]: getInboundInspectionSignables,
  [FormTemplate.INBD_EXT_COATING_INSP]: getInbdExteriorCoatingInspSignables,
}

function getFormSignables(...args: Parameters<GetSignables>) {
  return (
    SIGNABLES_GETTER_BY_TEMPLATE[args[0].template]?.(...args) ?? ['unknown']
  )
}

function getResponseOption(
  question: FormQuestion,
  responseId: number | undefined,
) {
  return question.responseOptions.find(({ id }) => responseId === id)
}

function getSelectedResponseOption(
  question: FormQuestion,
  response: FormResponse,
) {
  const selectedResponseId = response[question.id]?.responseId

  return (
    getResponseOption(question, selectedResponseId) ??
    question.responseOptions.find(({ isDefault }) => isDefault)
  )
}

const FormResponse = z.record(FormQuestionResponse.optional())

const FormSignatures = z.record(FormSignature.optional())

const FormContent = z.object({
  response: FormResponse.default({} as FormResponse),
  signatures: FormSignatures.default({}),
  signatureHistory: FormSignatureHistory.default({}),
  version: z.number(),
  createdDate: z.string().nullish(),
  lastModifiedDate: z.string().nullish(),
})

type FormSignature = z.infer<typeof FormSignature>
type FormSignatureType = z.infer<typeof FormSignatureType>
type FormSignatureHistory = z.infer<typeof FormSignatureHistory>
type FormSignatureHistoryEntryType = z.infer<
  typeof FormSignatureHistoryEntryType
>
type FormSignatureHistoryEntry = z.infer<typeof FormSignatureHistoryEntry>

type FormSignatures = z.infer<typeof FormSignatures>
type FormResponse = z.infer<typeof FormResponse>
type FormContent = z.infer<typeof FormContent>

const GLOBAL_FORM_SIGNATURE_ID = 'form'

export {
  createFormSignature,
  createFormSignatureHistoryEntry,
  FormContent,
  FormQuestionResponse,
  FormResponse,
  FormResponseData,
  FormResponseDataArray,
  FormResponseDataObject,
  FormSignature,
  FormSignatureHistory,
  FormSignatureHistoryEntry,
  FormSignatureHistoryEntryType,
  FormSignatures,
  FormSignatureType,
  FormStatusCode,
  FormTemplate,
  getFormResponseSchema,
  getFormSignables,
  getResponseOption,
  getSelectedResponseOption,
  GLOBAL_FORM_SIGNATURE_ID,
}
export type { Form, FormQuestion, FormResponseOption, RelatedFormItem }
