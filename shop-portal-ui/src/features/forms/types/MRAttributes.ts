export enum MRAttribute {
  STAR_STENCIL = 'Star Stencil',
  AAR_CODE = 'AAR_CODE',
  TYPE_OF_PART = 'TYPE_OF_PART',
}

/**
 * Maps ATTRIBUTE_NAME to the actual {FIELD_NAME}_MR (current data) field of the pattern
 */
export const MR_FIELD_BY_ATTRIBUTE: Record<string, string> = {
  [MRAttribute.STAR_STENCIL]: 'currentData_MR',
  [MRAttribute.AAR_CODE]: 'currentData_MR',
  [MRAttribute.TYPE_OF_PART]: 'currentData_MR',
}

/**
 * Maps ATTRIBUTE_NAME to the actual {FIELD_NAME} (correction) field of the pattern
 */

export const MR_RESPONSE_FIELD_BY_ATTRIBUTE: Record<string, string> = {
  [MRAttribute.STAR_STENCIL]: 'currentData',
  [MRAttribute.AAR_CODE]: 'currentData',
  [MRAttribute.TYPE_OF_PART]: 'currentData',
}
