import { useI18n } from '@gatx-corp/platform-one-common'
import {
  Heading,
  Landmark,
} from '@gatx-corp/platform-one-common/components/Content'
import { FC, useMemo } from 'react'
import FormQuestions from '../../components/FormQuestions'
import FormSignatureInput from '../../components/FormSignatureInput'
import {
  useForm,
  useFormSignatures,
  useValidateForm,
} from '../../providers/FormProvider'
import { GLOBAL_FORM_SIGNATURE_ID } from '../../types/Form'

const InboundInspection: FC = () => {
  const { t } = useI18n('Forms', { keyPrefix: 'Inbound Inspection' })
  const form = useForm()
  const validate = useValidateForm()
  const signatures = useFormSignatures()

  const isFormSignable = useMemo(
    () =>
      signatures.signables
        .filter((s) => s !== GLOBAL_FORM_SIGNATURE_ID)
        .every((s) => signatures.get(s)?.type === 'complete') && validate(),
    [signatures, validate],
  )

  return (
    <Landmark id="inbound-inspection-form" className="space-y-sm">
      <Heading className="text-title-xs mx-lg print:text-title-sm print:mx-0 print:rounded print:bg-primary-7 print:text-[white] print:w-full print:py-[4px] print:px-sm print:block">
        <span className="print:hidden">
          {t(form.shortName, {
            defaultValue: form.shortName,
            context: 'group',
          })}
        </span>
        <span className="hidden print:block">
          {t(form.shortName, {
            defaultValue: form.shortName,
          })}
        </span>
      </Heading>

      <FormQuestions />

      <div className="p-lg dark:bg-dark-6 bg-light-1 mt-md rounded shadow-[0px_2px_4px_0px_#00000013] print:hidden">
        <FormSignatureInput
          id={GLOBAL_FORM_SIGNATURE_ID}
          label={t('signatureLabel')}
          acknowledgment={t('signatureAcknowledgement')}
          signable={isFormSignable}
          unsignable={false}
          isFinal={true}
        />
      </div>
    </Landmark>
  )
}

export default InboundInspection
