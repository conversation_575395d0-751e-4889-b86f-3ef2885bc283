import { Form, GLOBAL_FORM_SIGNATURE_ID } from '@/features/forms/types/Form'

/**
 * Returns the ids of the signable sections for the 'Inbound Inspection' form.
 * This form requires a signature for the form itself and for each question.
 *
 * @param response - The response to the form.
 * @returns The ids of the signables
 */
function getInboundInspectionSignables(form: Form) {
  return [
    GLOBAL_FORM_SIGNATURE_ID,
    ...form.questions.map((q) => q.id.toString()),
  ]
}

export { getInboundInspectionSignables }
