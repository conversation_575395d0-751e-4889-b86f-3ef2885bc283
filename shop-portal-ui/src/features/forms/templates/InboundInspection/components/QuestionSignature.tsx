import {
  useFormQuestionStatus,
  useFormSignatures,
} from '@/features/forms/providers/FormProvider'
import { FormQuestion, FormSignature } from '@/features/forms/types/Form'
import { formatDate } from '@/utils/dates'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Button } from '@gatx-corp/platform-one-common/components/Button'
import {
  ChoiceGroup,
  type Option,
} from '@gatx-corp/platform-one-common/components/ChoiceGroup'
import { FC } from 'react'

type OptionValue = FormSignature['type']

type Props = {
  question: FormQuestion
}

const QuestionSignature: FC<Props> = ({ question }) => {
  const { t } = useI18n('Forms', { keyPrefix: 'Inbound Inspection' })
  const status = useFormQuestionStatus(question)
  const { get, sign, unsign } = useFormSignatures()

  const questionId = question.id.toString()

  const signatureType = get(questionId)?.type

  //
  // FIXME - at present we don't have a reliable way to determine when to enable
  // the "deferred" button. See this discussion for more information:
  //  * https://github.com/GATX-Corp/gatx-platform-one-shop-portal-ui/pull/233#discussion_r2153854786
  //
  const deferrable = false

  const optionComplete: Option<OptionValue> = {
    label: t('inspectedComplete'),
    value: 'complete',
  }

  const optionDeferred: Option<OptionValue> = {
    label: t('inspectedDeferred'),
    value: 'deferred',
  }

  const options = deferrable
    ? [optionDeferred, optionComplete]
    : [optionComplete]

  const signature = get(questionId)

  return (
    <div>
      <ChoiceGroup<OptionValue>
        label={t('inspected')}
        value={signatureType}
        options={options}
        multiSelect={false}
        disabled={status !== 'valid'}
        readOnly={signatureType !== undefined}
        onChange={(value) => {
          sign(questionId, value).catch((e) =>
            console.error(
              `Unable to "sign" Inbound Inspection question ${question.id}`,
              e,
            ),
          )
        }}
      />
      {signatureType && (
        <div>
          <div>
            {t('itemCompletedOrDeferredBy', {
              name: signature?.signedBy.name ?? 'unknown',
              dateString: signature
                ? formatDate(signature.signedAt)
                : 'unknown',
              action: t(`action.${signatureType}`),
            })}
          </div>
          <Button
            className="mt-lg"
            text={t('updateInspection')}
            variant="outline"
            onClick={() => {
              unsign(questionId).catch((e) =>
                console.error(
                  `Unable to "un-sign" Inbound Inspection question ${question.id}`,
                  e,
                ),
              )
            }}
          />
        </div>
      )}
    </div>
  )
}

export default QuestionSignature
