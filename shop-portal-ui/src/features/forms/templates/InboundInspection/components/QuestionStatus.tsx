import {
  useFormQuestionStatus,
  useFormSignatures,
} from '@/features/forms/providers/FormProvider'
import { FormQuestion } from '@/features/forms/types/Form'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Badge } from '@gatx-corp/platform-one-common/components/Badge'
import { FC } from 'react'

type Props = {
  question: FormQuestion
}

const QuestionStatus: FC<Props> = ({ question }) => {
  const { t } = useI18n('Forms')
  const status = useFormQuestionStatus(question)
  const { completed } = useFormSignatures()

  const isComplete = completed(question.id.toString())

  return status === 'not-started' ? (
    <></>
  ) : isComplete ? (
    <Badge variant="success" label={t('completed')} />
  ) : (
    <Badge variant="warning" label={t('inProgress')} />
  )
}

export default QuestionStatus
