import FormQuestion from '@/features/forms/components/FormQuestion'
import {
  useForm,
  useFormSignatures,
  useHiddenQuestionIds,
} from '@/features/forms/providers/FormProvider'
import { FormResponsePattern } from '@/features/forms/types/FormResponsePattern'
import { i18n, useI18n } from '@gatx-corp/platform-one-common'
import { Accordion } from '@gatx-corp/platform-one-common/components/Accordion'
import { Trans } from 'react-i18next'
import QuestionSignature from './QuestionSignature'
import QuestionStatus from './QuestionStatus'

const InboundInspectionQuestions = () => {
  const { t } = useI18n('Forms', { keyPrefix: 'Inbound Inspection' })
  const form = useForm()
  const signatures = useFormSignatures()

  const hiddenQuestionIds = useHiddenQuestionIds()

  const onPatternChange = (
    question: FormQuestion,
    pattern: FormResponsePattern | null,
  ) => {
    const questionId = question.id.toString()

    if (
      pattern === FormResponsePattern.NONE &&
      !signatures.get(questionId) &&
      signatures.signable(questionId)
    ) {
      signatures
        .sign(questionId, 'complete')
        .catch((e) =>
          console.error(`Could not sign question: ${questionId}: `, e),
        )
    }
  }
  const empty = hiddenQuestionIds.size === form.questions.length

  return (
    <>
      <div
        className="contents aria-hidden:hidden print:aria-hidden:contents"
        aria-hidden={empty}
      >
        <Accordion.Container
          variant="card"
          hasExpandCollapse
          screenReaderContext={t('sectionsTitle')}
        >
          {form.questions.map((question) => (
            <Accordion.Item
              key={question.id}
              title={question.question}
              indicators={<QuestionStatus question={question} />}
              className="aria-hidden:hidden print:aria-hidden:contents"
              aria-hidden={hiddenQuestionIds.has(question.id)}
            >
              <div className="customer-print:flex gap-lg px-md pt-md pb-0 flex flex-col print:p-0">
                <dl>
                  <dt className="whitespace-nowrap customer-print:hidden">
                    {t('inspectionMethod')}
                  </dt>
                  <dd>{t('inspectionMethod.visual')}</dd>
                </dl>
                <FormQuestion
                  question={question}
                  onPatternChange={(p) => onPatternChange(question, p)}
                />
                <QuestionSignature question={question} />
              </div>
            </Accordion.Item>
          ))}
        </Accordion.Container>
      </div>
      <div
        className="flex flex-col items-center justify-center h-full gap-sm px-lg py-2xl print:hidden aria-hidden:hidden"
        aria-hidden={!empty}
      >
        <Trans
          i18n={i18n}
          i18nKey="Inbound Inspection.filter.empty"
          ns="Forms"
          components={{
            title: <div className="text-title-rg" />,
            description: <div className="text-body-rg" />,
          }}
        />
      </div>
    </>
  )
}

export default InboundInspectionQuestions
