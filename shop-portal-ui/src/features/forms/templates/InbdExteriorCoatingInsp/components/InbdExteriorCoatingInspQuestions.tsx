import FileViewerButton from '@/features/documents/components/FileViewer/FileViewerButton'
import { ReferenceDocument } from '@/features/documents/types/ReferenceDocument'
import FormQuestion from '@/features/forms/components/FormQuestion'
import { FormSignatureBadge } from '@/features/forms/components/FormSignatureInput'
import {
  useForm,
  useFormRailcar,
  useFormReferenceDocuments,
  useFormSignatures,
} from '@/features/forms/providers/FormProvider'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Accordion } from '@gatx-corp/platform-one-common/components/Accordion'

const ProcedureButton = ({ document }: { document: ReferenceDocument }) => {
  const { t } = useI18n('Documents')
  const { carNumber } = useFormRailcar()

  return (
    <>
      <div className="print:hidden">
        <FileViewerButton
          railcarNumber={carNumber ?? ''}
          key={document.referenceDocId}
          documentType={document.referenceDocType}
          documentParams={{ documentNumber: document.referenceDoc }}
          label={t('procedureReferenceDoc', {
            referenceDoc: document.referenceDoc,
          })}
          variant="outline"
          size="medium"
        />
      </div>
      <div className="hidden print:block customer-print:px-sm customer-print:py-xs customer-print:bg-light-3 rounded w-full">
        <dt>{t('procedure')}</dt>
        <dd>{document.referenceDoc}</dd>
      </div>
    </>
  )
}

const InbdExteriorCoatingInspQuestions = () => {
  const { t } = useI18n('Forms', { keyPrefix: 'Inbd Exterior Coating Insp' })
  const form = useForm()
  const signatures = useFormSignatures()

  const [document] = useFormReferenceDocuments()

  return (
    <Accordion.Container variant="card" hasExpandCollapse expandFirstItem>
      <Accordion.Item
        title={t('title')}
        indicators={
          <FormSignatureBadge
            id={signatures.signables[0]}
            label={t('signatureLabel')}
          />
        }
      >
        <div className="px-md py-sm flex gap-sm items-start justify-between print:p-0 print:flex-col-reverse print:gap-md customer-print:gap-0">
          <ol className="space-y-2xl print:space-y-md customer-print:w-full customer-print:space-y-0">
            {form.questions.map((question) => (
              <li
                key={question.id}
                className="space-y-lg print:space-y-sm customer-print:space-y-0 even:customer-print:bg-light-3 rounded customer-print:px-sm customer-print:py-xs customer-print:w-full"
              >
                <dl>
                  <dt className="whitespace-nowrap customer-print:hidden">
                    {t('InspectionItem')}
                  </dt>
                  <dd>{question.question}</dd>
                </dl>
                <FormQuestion question={question} />
              </li>
            ))}
          </ol>
          {document && <ProcedureButton document={document} />}
        </div>
      </Accordion.Item>
    </Accordion.Container>
  )
}

export default InbdExteriorCoatingInspQuestions
