import { z } from 'zod'

import { Form, FormQuestionResponse } from '@/features/forms/types/Form'

enum CleaningConfirmationSectionId {
  CLEANING_INSPECTION = 'cleaningInspection',
  CLEANING_APPROVAL = 'cleaningApproval',
  CLEANING_INFORMATION = 'cleaningInformation',
  CLEANING_QA = 'cleaningQA',
}

const WithCleaningApproved = z.object({
  cleaningApproval: z.object({ confirmed: z.literal('approved') }),
})

function getCleaningConfirmationSignables(
  _form: Form,
  response: Partial<Record<string, FormQuestionResponse>>,
) {
  const [question = { data: {} }] = Object.values(response)

  const cleaningApproved = WithCleaningApproved.safeParse(question.data).success

  return [
    CleaningConfirmationSectionId.CLEANING_INSPECTION,
    CleaningConfirmationSectionId.CLEANING_APPROVAL,

    ...(cleaningApproved
      ? [
          CleaningConfirmationSectionId.CLEANING_INFORMATION,
          CleaningConfirmationSectionId.CLEANING_QA,
        ]
      : []),
  ]
}

export { CleaningConfirmationSectionId, getCleaningConfirmationSignables }
