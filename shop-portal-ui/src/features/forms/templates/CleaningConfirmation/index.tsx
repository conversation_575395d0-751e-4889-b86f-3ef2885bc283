import { useI18n } from '@gatx-corp/platform-one-common'
import {
  Heading,
  Landmark,
} from '@gatx-corp/platform-one-common/components/Content'
import { FC, useState } from 'react'
import { Collapse } from 'react-bootstrap'
import { ChevronDown, ChevronUp } from 'react-bootstrap-icons'
import FormQuestions from '../../components/FormQuestions'
import CleaningSummary from './components/CleaningSummary'

const CleaningConfirmation: FC = () => {
  const { t } = useI18n('Forms', { keyPrefix: 'CleaningForm' })

  const [showCleaningSummary, setShowCleaningSummary] = useState(true)
  const cleaningSummaryId = 'cleaning-summary'
  const ChevronIcon = showCleaningSummary ? ChevronUp : ChevronDown

  return (
    <Landmark id="cleaning-form" className="space-y-sm">
      <div className="flex justify-between items-center mx-lg print:mx-0">
        <Heading className="text-title-sm print:rounded print:bg-primary-7 print:text-[white] print:w-full print:py-[4px] print:px-sm print:block">
          {t('cleaningConfirmation')}
        </Heading>

        <button
          className="flex items-center gap-sm print:hidden"
          onClick={() => setShowCleaningSummary(!showCleaningSummary)}
          aria-label={t('showCleaningSummary')}
          aria-expanded={showCleaningSummary}
          aria-controls={cleaningSummaryId}
          data-show-text={t('showCleaningSummary')}
          data-hide-text={t('hideCleaningSummary')}
        >
          {showCleaningSummary
            ? t('hideCleaningSummary')
            : t('showCleaningSummary')}
          <ChevronIcon className="icon" aria-hidden />
        </button>
      </div>

      <Collapse in={showCleaningSummary}>
        <div id={cleaningSummaryId}>
          <CleaningSummary />
        </div>
      </Collapse>

      <div className="hidden print:block customer-print:hidden">
        {t('allFieldsRequired')}
      </div>
      <FormQuestions />
    </Landmark>
  )
}

export default CleaningConfirmation
