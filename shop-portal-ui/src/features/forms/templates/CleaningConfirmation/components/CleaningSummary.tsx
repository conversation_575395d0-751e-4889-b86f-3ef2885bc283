import DataField from '@/features/forms/components/DataField'
import {
  useFormRailcar,
  useFormServiceEvent,
} from '@/features/forms/providers/FormProvider'
import { booleanToYesNo } from '@/features/forms/utils'
import { useI18n } from '@gatx-corp/platform-one-common'

type CapacityValue = {
  primaryValue: string
  primaryUnit: string
  secondaryValue: string
  secondaryUnit: string
}

type Capacity = { name: string; value: string }

const calculateCapacity = (filteredCapacities: Capacity[]) => {
  const CAPACITY_MATCHER =
    /(?<primaryValue>[0-9,]+) (?<primaryUnit>.+) \((?<secondaryValue>[0-9,]+) (?<secondaryUnit>.+)\)/

  const capacities = filteredCapacities.map((c) => {
    return CAPACITY_MATCHER.exec(c.value)?.groups as CapacityValue
  })

  const { primaryUnit, secondaryUnit } = capacities[0]

  let primaryTotal = 0
  let secondaryTotal = 0

  capacities.forEach((c) => {
    primaryTotal += parseInt(c.primaryValue.replaceAll(',', ''))
    secondaryTotal += parseInt(c.secondaryValue.replaceAll(',', ''))
  })

  return `${primaryTotal.toLocaleString()} ${primaryUnit} (${secondaryTotal.toLocaleString()} ${secondaryUnit})`
}

const renderCapacity = (capacities: Capacity[]) => {
  switch (true) {
    case capacities.length > 1:
      return calculateCapacity(capacities)
    case capacities.length === 1:
      return capacities[0]?.value
    case capacities.length === 0:
      return 'N/A'
  }
}

const renderRestrictionCode = (
  restrictionCode: string | null,
  restrictionDescription: string | null,
) => {
  if (!restrictionCode) {
    return
  }

  if (!restrictionDescription) {
    return restrictionCode
  }

  return `${restrictionCode} - ${restrictionDescription}`
}

const CleaningSummary = () => {
  const { t } = useI18n('Forms', { keyPrefix: 'CleaningSummary' })
  const railcar = useFormRailcar()
  const serviceEvent = useFormServiceEvent()

  if (!railcar || !serviceEvent) {
    return null
  }

  const { commodityData, alerts, customer1: customer } = serviceEvent

  // TODO: We'd like for the shop code to come to us as part of the event payload
  const shopCode = railcar.serviceEvents.find(
    (event) => event.id === serviceEvent.serviceEventId,
  )?.shop

  const capacities = railcar.Capacities ?? []
  const filteredCapacities = capacities.filter((c) =>
    c.name.includes('Capacity'),
  )
  const capacity = renderCapacity(filteredCapacities)

  const hasDno = Boolean(alerts.find((alert) => alert.alertType === 'DNO'))
  const hasDne = Boolean(alerts.find((alert) => alert.alertType === 'DNE'))

  const commodity = commodityData.confirmed
  const restrictionCode = commodityData.restrictionCode
  const restrictionDescription = commodityData.restrictionDescription

  const reportedClean = booleanToYesNo(commodityData.reportedClean)
  const dno = booleanToYesNo(hasDno)
  const dne = booleanToYesNo(hasDne)

  return (
    <>
      <div className="print:hidden">
        <dl className="flex gap-2xl flex-wrap border border-light-7 bg-light-4 dark:bg-dark-7 dark:border-dark-5 rounded mt-lg p-lg">
          <DataField label={t('carNumber')}>{railcar.carNumber}</DataField>
          <DataField label={t('facility')}>{shopCode}</DataField>
          <DataField label={t('capacity')}>{capacity}</DataField>
          <DataField label={t('confirmedCIN')}>
            <div className="max-w-[280px]">
              {commodity.cinNumber} - {commodity.cinName}
            </div>
          </DataField>
          <DataField label={t('stcc')}>
            <div className="max-w-[280px]">
              {commodity.stccNumber} - {commodity.stccName}
            </div>
          </DataField>
          <DataField label={t('restrictionCode')}>
            {renderRestrictionCode(restrictionCode, restrictionDescription)}
          </DataField>
          <DataField label={t('sds')}>{commodityData.sdsDocument}</DataField>
          <DataField label={t('carReportedClean')}>{reportedClean}</DataField>
          <DataField label={t('dno')}>{dno}</DataField>
          <DataField label={t('dne')}>{dne}</DataField>
        </dl>
      </div>
      <div className="hidden print:block" aria-hidden>
        <div className="flex gap-lg bg-light-3 rounded py-xs px-sm">
          <DataField label={t('carNumber')}>{railcar.carNumber}</DataField>
          <DataField label={t('facility')}>{shopCode}</DataField>
          <DataField label={t('customer')}>{customer.name}</DataField>
          <DataField label={t('capacity')}>{capacity}</DataField>
          <div className="contents customer-print:hidden">
            <DataField label={t('rc')}>{restrictionCode}</DataField>
            <DataField label={t('sds')}>{commodityData.sdsDocument}</DataField>
          </div>
          <DataField label={t('carReportedClean')}>{reportedClean}</DataField>
          <DataField label={t('dno')}>{dno}</DataField>
          <DataField label={t('dne')}>{dne}</DataField>
        </div>
        <div className="flex gap-lg py-xs px-sm">
          <div className="contents customer-print:hidden">
            <DataField label={t('confirmedCIN')}>
              {commodity.cinNumber} - {commodity.cinName}
            </DataField>
          </div>
          <div className="hidden customer-print:contents">
            <DataField label={t('commodity')}>
              {commodity.cinNumber} - {commodity.cinName}
            </DataField>
          </div>
          <DataField label={t('stcc')}>
            {commodity.stccNumber} - {commodity.stccName}
          </DataField>
        </div>
      </div>
    </>
  )
}

export default CleaningSummary
