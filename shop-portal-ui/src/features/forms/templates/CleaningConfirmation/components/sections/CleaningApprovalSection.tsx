import { <PERSON><PERSON> } from '@/types/Json'

import FormChoiceGroupField from '@/features/forms/components/FormChoiceGroupField'
import { useFormSectionFieldProps } from '@/features/forms/patterns/cleaning-confirmation/hooks/useFormSectionFieldProps'
import { CleaningFormSectionId } from '@/features/forms/patterns/cleaning-confirmation/types/CleaningGeneral'
import { useFormRailcar } from '@/features/forms/providers/FormProvider'
import { useFormQuestionResponse } from '@/features/forms/providers/FormQuestionProvider'
import { FormResponseDataObject } from '@/features/forms/types/Form'

const ID = CleaningFormSectionId.CLEANING_APPROVAL

const CleaningApprovalSection = () => {
  const railcar = useFormRailcar()

  const fields = useFormSectionFieldProps(ID)
  const [, setQuestionResponse] = useFormQuestionResponse()

  function handleArrivedCleanChange(value: Json) {
    let confirmed = null

    if (fields.confirmed) {
      confirmed = fields.confirmed.value

      if (value === 'Yes' && !confirmed) confirmed = 'not-required'
      if (value === 'No' && confirmed === 'not-required') confirmed = null
    }

    if (fields.arrivedClean) {
      const { onChange: setArrivedClean } = fields.arrivedClean
      setArrivedClean(value)
    }

    setQuestionResponse((prev) => {
      const res = prev
      const values = FormResponseDataObject.parse(
        ID in res ? (res[ID] ?? {}) : {},
      )

      return {
        ...res,
        [ID]: {
          ...values,
          arrivedClean: value,
          confirmed,
        } as Record<string, Json>,
      }
    })
  }

  return (
    <div className="flex flex-col print:gap-sm">
      <p className="mb-lg print:mb-0">
        Based upon review of the data in the above sections, I certify that{' '}
        {railcar.carNumber}
      </p>
      <div className="flex flex-wrap gap-xl print:gap-x-xl print:gap-y-xs">
        {fields.arrivedClean && (
          <FormChoiceGroupField
            size="large"
            {...fields.arrivedClean}
            onChange={handleArrivedCleanChange}
          />
        )}

        {fields.confirmed && (
          <FormChoiceGroupField size="large" {...fields.confirmed} />
        )}

        {fields.approvalDetails && (
          <FormChoiceGroupField size="large" {...fields.approvalDetails} />
        )}

        {fields.rejectionDetails && (
          <FormChoiceGroupField size="large" {...fields.rejectionDetails} />
        )}
      </div>
    </div>
  )
}

export default CleaningApprovalSection
