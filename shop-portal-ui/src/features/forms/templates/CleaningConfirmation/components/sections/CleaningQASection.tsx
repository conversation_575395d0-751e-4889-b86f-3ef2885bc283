import DataField from '@/features/forms/components/DataField'
import Form<PERSON><PERSON>iceGroup<PERSON>ield from '@/features/forms/components/FormChoiceGroupField'
import FormNumberField from '@/features/forms/components/FormNumberField'
import { useFormSectionFieldProps } from '@/features/forms/patterns/cleaning-confirmation/hooks/useFormSectionFieldProps'
import { useFormQuestionResponse } from '@/features/forms/providers/FormQuestionProvider'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { z } from 'zod'
import { CleaningGeneralResponse } from '../../../../patterns/cleaning-confirmation/types/CleaningGeneral'
import { CleaningConfirmationSectionId } from '../../types/CleaningConfirmation'

const ID = CleaningConfirmationSectionId.CLEANING_QA

const StringOrDash = z.coerce
  .string()
  .nullable()
  .optional()
  .transform((s) => s ?? undefined)
  .default('—')

/**
 * Nitrogen and Air are cleaning method options that may come from the Forms Service.
 * Check the "Cleaning Method" field in the Cleaning Information schema to track where the request is done.
 */
const NITROGEN = 'Nitrogen'
const AIR = 'Air'

const CleaningQASection = () => {
  const { t } = useI18n('Forms', {
    keyPrefix: 'Cleaning Confirmation.cleaningQA',
  })

  const fields = useFormSectionFieldProps(ID)
  const [response] = useFormQuestionResponse<Partial<CleaningGeneralResponse>>()

  const nitrogenWarningAnswered = fields.NiApplied?.value !== undefined
  const nitrogenWarningApplied = fields.NiApplied!.value === 'Yes'

  const nitrogenUsed =
    !!response.cleaningInformation &&
    'cleaningMethods' in response.cleaningInformation &&
    response.cleaningInformation.cleaningMethods.includes(NITROGEN)

  const airUsed =
    !!response.cleaningInformation &&
    'cleaningMethods' in response.cleaningInformation &&
    response.cleaningInformation.cleaningMethods.includes(AIR)

  const nitrogenMismatch =
    nitrogenWarningAnswered &&
    ((nitrogenUsed && !airUsed && !nitrogenWarningApplied) ||
      (!nitrogenUsed && nitrogenWarningApplied))

  return (
    <>
      <div className="flex flex-col print:gap-sm customer-print:hidden">
        {nitrogenMismatch && (
          <Alert
            level="warning"
            urgency="inline"
            compact
            className="mb-lg print:hidden"
          >
            <span className="font-bold">{t('pleaseNote')}</span>
            <span className="text-subtle">
              {nitrogenWarningApplied
                ? t('niTagNotExpected')
                : t('niTagExpected')}
            </span>
          </Alert>
        )}
        <div className="flex flex-wrap gap-xl print:gap-sm print:grid print:grid-cols-4">
          {fields.LEL && <FormNumberField format="percent" {...fields.LEL} />}
          {fields.interiorInspectionPass && (
            <FormChoiceGroupField
              size="large"
              {...fields.interiorInspectionPass}
            />
          )}
          {fields.placardsRemoved && (
            <FormChoiceGroupField size="large" {...fields.placardsRemoved} />
          )}
          {fields.exteriorInspectionPass && (
            <FormChoiceGroupField
              size="large"
              {...fields.exteriorInspectionPass}
            />
          )}
          {fields.commodityStencilCovered && (
            <FormChoiceGroupField
              size="large"
              {...fields.commodityStencilCovered}
            />
          )}
          {fields.orangeTagApplied && (
            <FormChoiceGroupField size="large" {...fields.orangeTagApplied} />
          )}
          {fields.NiApplied && (
            <FormChoiceGroupField
              size="large"
              warning={nitrogenMismatch ? t('niMismatch') : undefined}
              {...fields.NiApplied}
            />
          )}
        </div>
      </div>
      <dl className="hidden customer-print:contents capitalize" aria-hidden>
        <div className="flex gap-lg bg-light-3 rounded py-xs px-sm">
          <DataField label={t('LEL')}>
            {StringOrDash.parse(fields.LEL?.value)}
          </DataField>
          <DataField label={t('interiorInspectionPass')}>
            {StringOrDash.parse(fields.interiorInspectionPass?.value)}
          </DataField>
          <DataField label={t('placardsRemoved')}>
            {StringOrDash.parse(fields.placardsRemoved?.value)}
          </DataField>
          <DataField label={t('exteriorInspectionPass')}>
            {StringOrDash.parse(fields.exteriorInspectionPass?.value)}
          </DataField>
          <DataField label={t('commodityStencilCovered')}>
            {StringOrDash.parse(fields.commodityStencilCovered?.value)}
          </DataField>
        </div>
        <div className="flex gap-lg py-xs px-sm">
          <DataField label={t('orangeTagApplied')}>
            {StringOrDash.parse(fields.orangeTagApplied?.value)}
          </DataField>
          <DataField label={t('NiApplied')}>
            {StringOrDash.parse(fields.NiApplied?.value)}
          </DataField>
        </div>
      </dl>
    </>
  )
}

export default CleaningQASection
