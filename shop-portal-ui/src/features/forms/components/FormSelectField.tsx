import { <PERSON><PERSON> } from '@/types/Json'
import { Size } from '@gatx-corp/platform-one-common/components/ChoiceControl'
import { FormField } from '@gatx-corp/platform-one-common/components/FormField'
import { FormSelect } from '@gatx-corp/platform-one-common/components/FormSelect'
import { Option } from '../types/FormOptions'
import FormFieldContainer from './FormFieldContainer'

type Props = {
  label: string
  labelScreenreaderOnly?: boolean
  value: Json | undefined
  onChange: (value: Json) => void
  error?: string
  warning?: string
  readOnly?: boolean
  options?: Option[]
  size?: Size
  printOptions?: Option[]
  hidden?: boolean
  disabled?: boolean
}

const FormSelectField = (props: Props) => {
  const {
    label,
    labelScreenreaderOnly,
    value,
    onChange,
    error,
    warning,
    readOnly,
    options = [],
    hidden = false,
    disabled = false,
  } = props

  if (!value && options.length === 1) {
    onChange(options[0].value)
  }

  return (
    <FormFieldContainer
      hidden={hidden}
      screen={
        <FormField
          label={label}
          labelScreenreaderOnly={labelScreenreaderOnly}
          error={error}
          warning={warning}
          readOnly={readOnly}
        >
          {/* TODO: Replace with an actual combobox component */}
          <FormSelect
            label={label}
            aria-label={label}
            options={options}
            value={value ? (value as string) : ''}
            onSelectionChange={onChange}
            disabled={disabled}
          />
        </FormField>
      }
      print={
        <FormField label={label} labelScreenreaderOnly={labelScreenreaderOnly}>
          <div
            className="data-[empty=false]:contents data-[empty=false]:blank-print:block w-[100px] h-[25px] text-center rounded-lg border border-light-9"
            data-empty={!value}
          >
            <span className="align-middle blank-print:hidden">
              {value as string}
            </span>
          </div>
        </FormField>
      }
    />
  )
}

export default FormSelectField
