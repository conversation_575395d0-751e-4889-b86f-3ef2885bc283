import { i18n } from '@gatx-corp/platform-one-common'
import { Heading } from '@gatx-corp/platform-one-common/components/Content'
import { Trans } from 'react-i18next'
import SelectFormLogo from './SelectFormLogo'

const SelectForm = () => {
  return (
    <div className="px-lg py-2xl flex flex-col items-center justify-center absolute inset-0">
      <SelectFormLogo className="size-[108px] mx-auto mb-2xl" />
      <Trans
        i18n={i18n}
        i18nKey="SelectForm"
        ns="Forms"
        components={{
          title: <Heading className="text-title-rg mb-sm" />,
          description: <p className="text-subtle text-body-rg" />,
        }}
      />
    </div>
  )
}

export default SelectForm
