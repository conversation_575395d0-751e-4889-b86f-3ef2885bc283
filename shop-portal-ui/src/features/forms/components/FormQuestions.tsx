import { FC } from 'react'
import { useForm } from '../providers/FormProvider'
import InbdExteriorCoatingInspQuestions from '../templates/InbdExteriorCoatingInsp/components/InbdExteriorCoatingInspQuestions'
import InboundInspectionQuestions from '../templates/InboundInspection/components/InboundInspectionQuestions'
import { FormTemplate } from '../types/Form'
import FormQuestion from './FormQuestion'

const FORM_QUESTION_COMPONENTS: Partial<Record<FormTemplate, FC>> = {
  [FormTemplate.INBOUND_INSPECTION]: InboundInspectionQuestions,
  [FormTemplate.INBD_EXT_COATING_INSP]: InbdExteriorCoatingInspQuestions,
}

const Fallback = () => {
  const form = useForm()

  return (
    <ol className="space-y-md translate-z-0">
      {form.questions.map((question) => (
        <li key={question.id}>
          <FormQuestion question={question} />
        </li>
      ))}
    </ol>
  )
}

const FormQuestions = () => {
  const form = useForm()
  const Component = FORM_QUESTION_COMPONENTS[form.template] ?? Fallback

  return <Component />
}

export default FormQuestions
