import { RouterLink } from '@/app/components/RouterLink'
import { useAppContext } from '@/providers/AppProvider'
import createUrl from '@/utils/createUrl'
import { useI18n } from '@gatx-corp/platform-one-common'
import { StatusTab } from '@gatx-corp/platform-one-common/components/StatusTab'
import { useSearchParams } from 'react-router-dom'
import { useForm } from '../hooks/useForm'
import { useFormDraft } from '../hooks/useFormDraft'
import {
  Form,
  FormContent,
  FormStatusCode,
  FormTemplate,
  RelatedFormItem,
} from '../types/Form'

const VARIANT_BY_STATUS: Record<
  FormStatusCode,
  'warning' | 'success' | 'info'
> = {
  [FormStatusCode.IN_PROCESS]: 'warning',
  [FormStatusCode.SIGNED]: 'success',
  [FormStatusCode.NOT_STARTED]: 'info',
  [FormStatusCode.DEACTIVATED]: 'info',
  [FormStatusCode.ARCHIVED]: 'info',
}

type Props<F> = {
  /**
   * The related form.
   */
  form: F
  /**
   * Returns a quantity of interest to display in the related form's status tab.
   * Usually used to display the number of questions in a form that comply with the filters applied to the current form.
   *
   * @param form - The full form specification.
   * @param content - The form's content.
   * @returns A quantity to be displayed in the related form's status tab.
   */
  getCount?: (form: Form, content: FormContent) => number
}

const RelatedForm = ({
  form,
  getCount,
  template,
}: Props<RelatedFormItem> & { template: FormTemplate }) => {
  const { serviceEventId, carNumber = '', formId = '' } = useAppContext()

  const [searchParams] = useSearchParams()

  const { data: relatedForm } = useForm({ formId: form.id })
  const [draft] = useFormDraft(form.id)

  const { t } = useI18n('Forms')

  const count = relatedForm && getCount?.(relatedForm, draft)

  const variant = VARIANT_BY_STATUS[form.status]
  const status = variant !== 'info' ? t(`status.${form.status}`) : undefined

  const href = `${createUrl({ carNumber, serviceEventId, formId: form.id })}?${searchParams.toString()}`

  return (
    <StatusTab
      href={href}
      label={t(`${template}.${form.name}`, {
        defaultValue: form.name,
        context: 'nav',
      })}
      selected={formId === form.id}
      status={status}
      variant={variant}
      secondary={count ? `${count}` : undefined}
      Link={RouterLink}
    />
  )
}

function toRelatedFormItem(form: Form): RelatedFormItem {
  return {
    id: form.id,
    name: form.shortName,
    status: form.statusCode,
    sequence: form.sequence,
  }
}

const RelatedForms = ({ form, ...props }: Props<Form>) => {
  const { t } = useI18n('Forms')
  const { relatedForms } = form

  const forms = [toRelatedFormItem(form), ...relatedForms].sort(
    (a, b) => a.sequence - b.sequence,
  )

  return (
    <nav
      aria-label={t(`${form.template}.title`, { defaultValue: form.shortName })}
    >
      <ul className="flex flex-row flex-wrap gap-md">
        {forms.map((item) => (
          <li key={item.id}>
            <RelatedForm form={item} {...props} template={form.template} />
          </li>
        ))}
      </ul>
    </nav>
  )
}

export default RelatedForms
