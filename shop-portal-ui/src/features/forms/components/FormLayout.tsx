import { FC, Fragment } from 'react'
import InboundInspectionLayout from '../templates/InboundInspection/components/InboundInspectionLayout'
import { Form, FormQuestion, FormTemplate } from '../types/Form'

type Props = {
  form: Form
  children: React.ReactElement<{ hiddenQuestionIds: Set<FormQuestion['id']> }>
}

const Fallback = ({ children }: Props) => <Fragment>{children}</Fragment>

const FORM_LAYOUTS: Partial<Record<FormTemplate, FC<Props> | undefined>> = {
  [FormTemplate.INBOUND_INSPECTION]: InboundInspectionLayout,
}

const FormLayout = ({ form, children }: Props) => {
  const Component = FORM_LAYOUTS[form.template] ?? Fallback
  return <Component form={form}>{children}</Component>
}

export default FormLayout
