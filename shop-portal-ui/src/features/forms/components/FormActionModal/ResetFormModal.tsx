import { useI18n } from '@gatx-corp/platform-one-common'
import { Button } from '@gatx-corp/platform-one-common/components/Button'
import { ModalOverlay } from '@gatx-corp/platform-one-common/components/ModalOverlay'

const FORM_ID = 'reset-form'

type Props = {
  showModal: boolean
  onHide: () => void
  onSave: () => void
}

const ResetFormModal = ({ showModal, onHide, onSave }: Props) => {
  const { t } = useI18n('Forms')

  return (
    <ModalOverlay
      title={t('Modal.resetForm')}
      show={showModal}
      onHide={onHide}
      size="small"
      hasCloseButton
      footer={
        <div className="flex justify-end gap-sm">
          <Button
            text={t('common.cancel')}
            onClick={onHide}
            variant="outline"
          />
          <Button
            form={FORM_ID}
            type="submit"
            text={t('Modal.resetForm')}
            onClick={() => {
              onSave()
              onHide()
            }}
          />
        </div>
      }
    >
      <form name={FORM_ID} className="mx-lg mt-md mb-xl">
        <span id="form-description" className="text-subtle text-body-rg">
          {t('Modal.resetFormModalConfirmation')}
        </span>
      </form>
    </ModalOverlay>
  )
}

export default ResetFormModal
