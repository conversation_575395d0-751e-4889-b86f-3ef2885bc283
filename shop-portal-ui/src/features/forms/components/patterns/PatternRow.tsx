import { useI18n } from '@gatx-corp/platform-one-common'
import { But<PERSON> } from '@gatx-corp/platform-one-common/components/Button'
import { ReactNode } from 'react'

type Props = {
  onRemove?: () => void
  children: ReactNode
  completed: boolean
}

const PatternRow = ({ onRemove, children, completed }: Props) => {
  const { t } = useI18n('Forms')

  return (
    <li className="flex flex-wrap gap-xl print:gap-x-xl print:gap-y-xs">
      <div className="flex">
        {children}
        {onRemove && !completed && (
          <Button
            text={t('common.removeRow')}
            iconOnly
            icon="XLg"
            variant="neutral"
            aria-label={t('common.removeRow')}
            className="ml-lg mt-[32px] h-[40px]"
            onClick={() => onRemove?.()}
          />
        )}
      </div>
    </li>
  )
}

export default PatternRow
