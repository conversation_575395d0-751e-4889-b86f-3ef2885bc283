import {
  useFormQuestion,
  useFormQuestionResponse,
} from '@/features/forms/providers/FormQuestionProvider'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Button } from '@gatx-corp/platform-one-common/components/Button'
import { ComponentType } from 'react'
import { useFormSignatures } from '../../providers/FormProvider'
import { FormResponseDataArray } from '../../types/Form'
import PatternRow from './PatternRow'

type RowComponentProps = { index: number }

type Props = {
  RowComponent: ComponentType<RowComponentProps>
}

const PatternWithRows = ({ RowComponent }: Props) => {
  const { t } = useI18n('Forms')
  const [response, setResponse] = useFormQuestionResponse()

  const question = useFormQuestion()
  const { completed } = useFormSignatures()
  const isComplete = completed(question.id.toString())
  const rows = FormResponseDataArray.parse(response ?? [{}])

  const removeRow = (index: number) => {
    if (!rows) return
    const newResponse = rows.filter((_, i) => i !== index)
    setResponse(newResponse)
  }

  return (
    <div className="flex flex-col">
      <ul>
        {rows.map((_, index) => (
          <PatternRow
            key={index}
            onRemove={rows.length > 1 ? () => removeRow(index) : undefined}
            completed={isComplete}
          >
            <RowComponent index={index} />
          </PatternRow>
        ))}
      </ul>

      {!isComplete && (
        <Button
          text={t('common.addRow')}
          variant="neutral"
          className="w-[100px] ml-auto pl-lg print:hidden"
          onClick={() => {
            const newResponse = rows ? [...rows, {}] : [{}]
            setResponse(newResponse)
          }}
        />
      )}
    </div>
  )
}

export default PatternWithRows
