import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { Form, FormStatusCode } from '../types/Form'

type Props = {
  form: Form
}

const FormStatusBanner = ({ form }: Props) => {
  const { t } = useI18n('Forms', { keyPrefix: 'FormStatusBanner' })
  const formStatus = form.statusCode

  if (
    formStatus === FormStatusCode.ARCHIVED ||
    formStatus === FormStatusCode.DEACTIVATED
  ) {
    return (
      <Alert
        level="warning"
        title={t('title', { context: formStatus })}
        urgency="deferred"
        compact
        className="mb-xl mt-md"
      >
        {t('message', { context: formStatus })}
      </Alert>
    )
  }
}

export default FormStatusBanner
