import { getOptions } from '@/features/forms/api/getOptions'
import number from '@/features/forms/types/FormNumber'
import options from '@/features/forms/types/FormOptions'
import { z } from 'zod'

const InspectionPoint = options().async(
  async (query: Record<string, unknown>) => {
    return await getOptions('InspectionPoint', query)
  },
)
const JobCode = options().async(
  async (query: Record<string, unknown>) => await getOptions('JobCode', query),
)
const Qualifier = options().async(
  async (query: Record<string, unknown>) =>
    await getOptions('Qualifier', query),
)

const WhyMade = options().async(
  async (query: Record<string, unknown>) => await getOptions('WhyMade', query),
)

const GeneralResponseRow = z.object({
  inspectionPoint: InspectionPoint,
  jobCode: JobCode,
  qualifier: Qualifier,
  whyMade: WhyMade,
  quantity: number().min(0).max(999).step(0.01),
})

const GeneralResponse = z.array(GeneralResponseRow).min(1)

type GeneralResponse = z.infer<typeof GeneralResponse>

export { GeneralResponse }
