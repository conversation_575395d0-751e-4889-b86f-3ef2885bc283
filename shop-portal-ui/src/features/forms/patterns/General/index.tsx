import PatternWithRows from '@/features/forms/components/patterns/PatternWithRows'
import { useI18n } from '@gatx-corp/platform-one-common'
import GeneralRow from './components/GeneralRow'

const General = () => {
  const { t } = useI18n('Forms', {
    keyPrefix: 'General',
  })

  return (
    <>
      <div className="mt-lg text-body-xs">{t('subtitle')}</div>
      <PatternWithRows RowComponent={GeneralRow} />
    </>
  )
}

export default General
