import FormSelectField from '@/features/forms/components/FormSelectField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { z } from 'zod'
import FormNumberField from '../../../components/FormNumberField'

const WithInspectionPointMetadata = z.object({
  metadata: z.object({ inspectionPointId: z.number() }),
})

type Props = {
  index: number
}

const GeneralRow = ({ index }: Props) => {
  const fields = useFormFields({ path: [index.toString()], signable: true })

  const inspectionPointId = fields.inspectionPoint?.options?.find(
    (option) => option.label === fields.inspectionPoint?.value,
  )?.metadata.id

  const filteredOptions = (field: string) => {
    return fields[field]?.options?.filter((option) => {
      const o = WithInspectionPointMetadata.safeParse(option.metadata)

      if (o.success) {
        return o.data.metadata.inspectionPointId === inspectionPointId
      }
    })
  }

  const jobCodeOptions = filteredOptions('jobCode')
  const qualifierOptions = filteredOptions('qualifier')
  const whyMadeOptions = filteredOptions('whyMade')

  return (
    <div className="mb-lg flex flex-wrap gap-xl print:gap-x-xl print:gap-y-xs">
      {fields.inspectionPoint && (
        <FormSelectField {...fields.inspectionPoint} />
      )}
      {fields.jobCode && (
        <FormSelectField
          {...fields.jobCode}
          options={jobCodeOptions}
          disabled={!fields.inspectionPoint?.value}
        />
      )}
      {fields.qualifier && (
        <FormSelectField
          {...fields.qualifier}
          options={qualifierOptions}
          disabled={!fields.inspectionPoint?.value}
        />
      )}
      {fields.whyMade && (
        <FormSelectField
          {...fields.whyMade}
          options={whyMadeOptions}
          disabled={!fields.inspectionPoint?.value}
        />
      )}
      {fields.quantity && <FormNumberField {...fields.quantity} />}
    </div>
  )
}

export default GeneralRow
