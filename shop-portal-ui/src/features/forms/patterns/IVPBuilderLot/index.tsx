import FormChoiceGroupField from '@/features/forms/components/FormChoiceGroupField'
import FormTextField from '@/features/forms/components/FormTextField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { FALSE, NO, TRUE } from '../../types/FormSchemaConstants'

const IVPBuilderLot = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ signable: true })

  console.log('fields', fields)
  console.log('fields.lotNumber_reply', fields.lotNumber_reply)

  return (
    <fieldset>
      {fields.hasMRData?.value === FALSE && (
        <>
          <Alert
            title={t('noMRData')}
            urgency="immediate"
            level="warning"
            compact
          >
            {t('noMRDataText')}
          </Alert>

          <div className="flex mt-xl">
            {fields.builderName && (
              <FormTextField
                {...fields.builderName}
                label={t('IVPBuilderLot.builderName_MR')}
              />
            )}
          </div>
        </>
      )}

      {fields.hasMRData?.value === TRUE && (
        <>
          {fields.allCorrect && (
            <FormChoiceGroupField size="large" {...fields.allCorrect} />
          )}

          <div className="flex gap-2xl mt-xl">
            <FormTextField
              readOnly
              value={
                fields.builderName_MR?.value
                  ? (fields.builderName_MR.value as string)
                  : t('none')
              }
              label={t('IVPBuilderLot.builderName_MR')}
              onChange={() => undefined}
            />

            {fields.allCorrect?.value === NO && fields.builderName_reply && (
              <FormChoiceGroupField
                size="large"
                {...fields.builderName_reply}
              />
            )}

            {fields.builderName_reply?.value && fields.builderName && (
              <FormTextField
                {...fields.builderName}
                label={t('IVPBuilderLot.correction')}
              />
            )}
          </div>
          <div className="flex gap-2xl mt-xl">
            <FormTextField
              readOnly
              value={
                fields.lotNumber_MR?.value
                  ? (fields.lotNumber_MR.value as string)
                  : t('none')
              }
              label={t('IVPBuilderLot.lotNumber_MR')}
              onChange={() => undefined}
            />

            {fields.allCorrect?.value === NO && fields.lotNumber_reply && (
              <FormChoiceGroupField size="large" {...fields.lotNumber_reply} />
            )}

            {fields.lotNumber_reply?.value && fields.lotNumber && (
              <FormTextField
                {...fields.lotNumber}
                label={t('IVPBuilderLot.correction')}
              />
            )}
          </div>
        </>
      )}
    </fieldset>
  )
}

export default IVPBuilderLot
