import branch, { merge } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { HAS_MR_DATA } from '@/features/forms/types/FormSchemaConstants'
import { z } from 'zod'
import { WithMRData, WithNoMRData } from '../../types/MRData'

const IVPBuilderLot = branch(
  [HAS_MR_DATA],
  WithNoMRData.extend({
    builderName: z.string(),
    lotNumber: z.string(),
  }),
  merge(
    WithMRData,
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      z
        .object({
          allCorrect: No,
          builderName_reply: options('Yes', "Can't Tell", 'Change'),
          lotNumber_reply: options('Yes', "Can't Tell", 'Change'),
          builderName: z.string().optional(),
          lotNumber: z.string().optional(),
        })
        .refine(
          (data) => {
            // If builderName_reply is 'Change', builderName must be provided
            if (data.builderName_reply === 'Change' && !data.builderName) {
              return false
            }
            // If lotNumber_reply is 'Change', lotNumber must be provided
            if (data.lotNumber_reply === 'Change' && !data.lotNumber) {
              return false
            }
            // If builderName_reply is not 'Change', builderName should not be provided
            if (data.builderName_reply !== 'Change' && data.builderName) {
              return false
            }
            // If lotNumber_reply is not 'Change', lotNumber should not be provided
            if (data.lotNumber_reply !== 'Change' && data.lotNumber) {
              return false
            }
            return true
          },
          {
            message: "Fields must be provided only when 'Change' is selected",
          },
        ),
    ),
  ),
)
type IVPBuilderLot = z.infer<typeof IVPBuilderLot>

export { IVPBuilderLot }
