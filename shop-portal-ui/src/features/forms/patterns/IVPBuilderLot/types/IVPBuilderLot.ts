import branch, { merge } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { HAS_MR_DATA } from '@/features/forms/types/FormSchemaConstants'
import { z } from 'zod'
import { WithMRData, WithNoMRData } from '../../types/MRData'

const IVPBuilderLot = branch(
  [HAS_MR_DATA],
  WithNoMRData.extend({
    builderName: z.string(),
    lotNumber: z.string(),
  }),
  merge(
    WithMRData,
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      branch(
        ['builderName_reply'],
        // When builderName_reply is NOT 'Change'
        branch(
          ['lotNumber_reply'],
          z.object({
            allCorrect: No,
            builderName_reply: options('Yes', "Can't Tell"),
            lotNumber_reply: options('Yes', "Can't Tell"),
          }),
          z.object({
            allCorrect: No,
            builderName_reply: options('Yes', "Can't Tell"),
            lotNumber_reply: options('Change'),
            lotNumber: z.string(), // Required when lotNumber_reply is 'Change'
          }),
        ),
        // When builderName_reply IS 'Change'
        branch(
          ['lotNumber_reply'],
          z.object({
            allCorrect: No,
            builderName_reply: options('Change'),
            builderName: z.string(), // Required when builderName_reply is 'Change'
            lotNumber_reply: options('Yes', "Can't Tell"),
          }),
          z.object({
            allCorrect: No,
            builderName_reply: options('Change'),
            builderName: z.string(), // Required when builderName_reply is 'Change'
            lotNumber_reply: options('Change'),
            lotNumber: z.string(), // Required when lotNumber_reply is 'Change'
          }),
        ),
      ),
    ),
  ),
)
type IVPBuilderLot = z.infer<typeof IVPBuilderLot>

export { IVPBuilderLot }
