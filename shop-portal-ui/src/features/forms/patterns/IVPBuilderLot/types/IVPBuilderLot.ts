import branch, { merge } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { HAS_MR_DATA } from '@/features/forms/types/FormSchemaConstants'
import { z } from 'zod'
import { WithMRData, WithNoMRData } from '../../types/MRData'

const IVPBuilderLot = branch(
  [HAS_MR_DATA],
  WithNoMRData.extend({
    builderName: z.string(),
    lotNumber: z.string(),
  }),
  merge(
    WithMRData,
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      z.intersection(
        z.object({
          allCorrect: No,
        }),
        z.intersection(
          branch(
            ['builderName_reply'],
            z.object({
              builderName_reply: options('Yes', "Can't Tell"),
            }),
            z.object({
              builderName_reply: options('Change'),
              builderName: z.string(),
            }),
          ),
          branch(
            ['lotNumber_reply'],
            z.object({
              lotNumber_reply: options('Yes', "Can't Tell"),
            }),
            z.object({
              lotNumber_reply: options('Change'),
              lotNumber: z.string(),
            }),
          ),
        ),
      ),
    ),
  ),
)
type IVPBuilderLot = z.infer<typeof IVPBuilderLot>

export { IVPBuilderLot }
