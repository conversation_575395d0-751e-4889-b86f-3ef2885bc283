import branch, { merge } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { HAS_MR_DATA } from '@/features/forms/types/FormSchemaConstants'
import { z } from 'zod'
import { WithMRData, WithNoMRData } from '../../types/MRData'

const IVPBuilderLot = branch(
  [HAS_MR_DATA],
  WithNoMRData.extend({
    builderName: z.string(),
    lotNumber: z.string(),
  }),
  merge(
    WithMRData,
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      branch(
        ['builderName_reply'],
        // builderName_reply: Yes
        branch(
          ['lotNumber_reply'],
          z.object({
            allCorrect: No,
            builderName_reply: options('Yes'),
            lotNumber_reply: options('Yes'),
          }),
          z.object({
            allCorrect: No,
            builderName_reply: options('Yes'),
            lotNumber_reply: options("Can't Tell"),
          }),
          z.object({
            allCorrect: No,
            builderName_reply: options('Yes'),
            lotNumber_reply: options('Change'),
            lotNumber: z.string(),
          }),
        ),
        // builderName_reply: Can't Tell
        branch(
          ['lotNumber_reply'],
          z.object({
            allCorrect: No,
            builderName_reply: options("Can't Tell"),
            lotNumber_reply: options('Yes'),
          }),
          z.object({
            allCorrect: No,
            builderName_reply: options("Can't Tell"),
            lotNumber_reply: options("Can't Tell"),
          }),
          z.object({
            allCorrect: No,
            builderName_reply: options("Can't Tell"),
            lotNumber_reply: options('Change'),
            lotNumber: z.string(),
          }),
        ),
        // builderName_reply: Change
        branch(
          ['lotNumber_reply'],
          z.object({
            allCorrect: No,
            builderName_reply: options('Change'),
            builderName: z.string(),
            lotNumber_reply: options('Yes'),
          }),
          z.object({
            allCorrect: No,
            builderName_reply: options('Change'),
            builderName: z.string(),
            lotNumber_reply: options("Can't Tell"),
          }),
          z.object({
            allCorrect: No,
            builderName_reply: options('Change'),
            builderName: z.string(),
            lotNumber_reply: options('Change'),
            lotNumber: z.string(),
          }),
        ),
      ),
    ),
  ),
)
type IVPBuilderLot = z.infer<typeof IVPBuilderLot>

export { IVPBuilderLot }
