import branch, { merge } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { HAS_MR_DATA } from '@/features/forms/types/FormSchemaConstants'
import { z } from 'zod'
import { WithMRData, WithNoMRData } from '../../types/MRData'

const IVPBuilderLot = branch(
  [HAS_MR_DATA],
  WithNoMRData.extend({
    builderName: z.string(),
    lotNumber: z.string(),
  }),
  merge(
    WithMRData,
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      z
        .object({
          allCorrect: No,
          builderName_reply: options('Yes', "Can't Tell", 'Change'),
          lotNumber_reply: options('Yes', "Can't Tell", 'Change'),
        })
        .extend({
          builderName: z.string().optional(),
          lotNumber: z.string().optional(),
        })
        .refine(
          (data) => {
            // builderName is required when builderName_reply is 'Change'
            if (data.builderName_reply === 'Change') {
              return !!data.builderName
            }
            // builderName should not be present when builderName_reply is not 'Change'
            return data.builderName === undefined
          },
          {
            message: "builderName is required when 'Change' is selected",
            path: ['builderName'],
          },
        )
        .refine(
          (data) => {
            // lotNumber is required when lotNumber_reply is 'Change'
            if (data.lotNumber_reply === 'Change') {
              return !!data.lotNumber
            }
            // lotNumber should not be present when lotNumber_reply is not 'Change'
            return data.lotNumber === undefined
          },
          {
            message: "lotNumber is required when 'Change' is selected",
            path: ['lotNumber'],
          },
        ),
    ),
  ),
)
type IVPBuilderLot = z.infer<typeof IVPBuilderLot>

export { IVPBuilderLot }
