import {
  Integer,
  Percentage,
  PositiveFloat,
  PositiveInteger,
} from '@/features/forms/types/Field'
import branch, { merge } from '@/features/forms/types/FormBranch'
import options from '@/features/forms/types/FormOptions'
import { z } from 'zod'
import { CleaningConfirmationSectionId } from '../../../templates/CleaningConfirmation/types/CleaningConfirmation'
import { WithCleaningInspection } from './sections/CleaningInspection'
import { CommonCleaningQA } from './sections/CleaningQA'

const CleaningHighPressureQA = CommonCleaningQA.extend({
  LEL: Percentage.nullish(),
})

const CleaningHighPressureInspection = merge(
  z.object({
    pressure: PositiveInteger,
    temperature: Integer.min(-60).max(150),
    ambientTemperature: Integer.min(-60).max(150).optional(),
    HPInCar: options('Yes', 'No'),
  }),
  branch(
    ['QGtest'],
    z.object({
      QGtest: options('Pass', 'Fail'),
      QGTtubeUsed: z.string(),
      QGTppm: PositiveFloat,
    }),
    z.object({
      QGtest: options('Not Tested'),
    }),
  ),
)

const CleaningHighPressureResponse = merge(
  z.object({
    cleaningInspection: CleaningHighPressureInspection,
    cleaningQA: CleaningHighPressureQA,
    cleaningInformation: z.object({
      totalFlareHours: PositiveFloat.nullish(),
    }),
  }),
  WithCleaningInspection,
)

type CleaningHighPressureResponse = z.infer<typeof CleaningHighPressureResponse>

export {
  CleaningConfirmationSectionId as CleaningFormSectionId,
  CleaningHighPressureResponse,
}
