import branch, { merge } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { Remove } from '@/features/forms/types/FormRemove'
import { z } from 'zod'
import { ExtraCleaning, ProtocolaryCleaning } from './CleaningInformation'

const CleaningApproved = z.object({
  confirmed: options('approved'),
  approvalDetails: options('Per EGP-05 Guideline', 'Per Customer'),
})

const CleaningRejected = z.object({
  confirmed: options('rejected'),
  rejectionDetails: options('Handle DNO/DNE', 'Reject to Customer'),
})

const CleaningNotRequired = z.object({
  confirmed: options('not-required'),
})

const ArrivedClean = z.object({
  arrivedClean: Yes,
})

const DidNotArriveClean = z.object({
  arrivedClean: No,
})

const CleaningApprovalResponse = branch(
  ['arrivedClean'],
  merge(
    z.object({
      arrivedClean: Yes,
    }),
    branch(
      ['confirmed'],
      CleaningApproved,
      CleaningRejected,
      CleaningNotRequired,
    ),
  ),
  merge(
    z.object({
      arrivedClean: No,
    }),
    branch(['confirmed'], CleaningApproved, CleaningRejected),
  ),
)

const WithCleaningApproved = z.object({
  cleaningApproval: CleaningApproved,
  cleaningInformation: ProtocolaryCleaning,
})

const WithCleaningRejected = z.object({
  cleaningApproval: CleaningRejected,
  cleaningInformation: Remove,
  cleaningQA: Remove,
})

const WithCleaningNotRequired = z.object({
  cleaningApproval: CleaningNotRequired,
  cleaningInformation: ExtraCleaning.extend({
    totalFlareHours: Remove,
  }),
})

const WithCleaningApproval = branch(
  ['cleaningApproval', 'arrivedClean'],
  merge(
    z.object({
      cleaningApproval: ArrivedClean,
    }),
    branch(
      ['cleaningApproval', 'confirmed'],
      WithCleaningApproved,
      WithCleaningRejected,
      WithCleaningNotRequired,
    ),
  ),
  merge(
    z.object({
      cleaningApproval: DidNotArriveClean,
    }),
    branch(
      ['cleaningApproval', 'confirmed'],
      WithCleaningApproved,
      WithCleaningRejected,
    ),
  ),
)

export { CleaningApprovalResponse, WithCleaningApproval }
