import { z } from 'zod'

import { getOptions } from '@/features/forms/api/getOptions'
import options from '@/features/forms/types/FormOptions'

const OPTION_LIST_ID = 'CleaningMethod'

const CleaningSpec = options('reviewed', 'none')

const CleaningMethods = options()
  .multi()
  .async(() => getOptions(OPTION_LIST_ID))

const CleaningPerformed = options(
  'Car is Clean - Vent Only',
  'Minor Cleaning of Clean Car',
  'Exterior Cleaning',
  'No Cleaning',
).multi()

const CleanedPer = options('gatx_recipe', 'customer_spec')

const ProtocolaryCleaning = z.object({
  cleaningSpecType: CleaningSpec,
  cleanedPer: CleanedPer,
  cleaningMethods: CleaningMethods,
})

const ExtraCleaning = z.object({
  cleaningSpecType: CleaningSpec,
  cleaningPerformed: CleaningPerformed,
})

export { ExtraCleaning, ProtocolaryCleaning }
