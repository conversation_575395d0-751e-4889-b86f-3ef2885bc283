import { _setupCachingForTests } from '@/features/caching/providers'
import {
  _ClientDBRxDatabase,
  _setupClientDBForTests,
  _setupRxDB,
} from '@/features/client-db/providers'
import * as listActions from '@/features/forms/api/getListOptions'
import { resolveSchema } from '@/features/forms/utils/schema'
import { CleaningOtherResponse } from './CleaningOther'

jest.mock('@/features/forms/api/getListOptions')

beforeAll(async () => {
  jest.spyOn(listActions, 'getListOptions').mockResolvedValue({
    success: true,
    response: [
      { id: 1, value: 'Nitrogen', label: 'Nitrogen', metadata: {}, list: '' },
    ],
  })

  _setupClientDBForTests(await _setupRxDB(true))
  _setupCachingForTests()

  await resolveSchema(CleaningOtherResponse)
})

afterAll(async () => {
  await _ClientDBRxDatabase?.destroy()
  jest.clearAllMocks()
})

describe('CleaningOther (Pattern Schema)', () => {
  it.each([
    'cleaningInspection',
    'cleaningInformation',
    'cleaningApproval',
    'cleaningQA',
  ] as const)('fails to parse data without "%s" section', (section) => {
    const DATA = {
      cleaningInspection: {},
      cleaningInformation: {},
      cleaningApproval: {},
      cleaningQA: {},
    }

    delete DATA[section]

    expect(CleaningOtherResponse.safeParse(DATA).success).toBeFalsy()
  })

  describe('when measurement is "Inches"', () => {
    function data(): CleaningOtherResponse {
      return {
        cleaningInspection: {
          measurement: 'inches',
          depthOfCommodity: 1,
          exteriorCleanRequired: 'Yes',
          totalGallons: 1,
          ventOnly: 'Yes',
        },
        cleaningInformation: {
          cleanedPer: 'gatx_recipe',
          cleaningMethods: ['Nitrogen'],
          cleaningSpecType: 'reviewed',
          totalGalRemoved: 1,
          drumLocation: 'Back',
        },
        cleaningApproval: {
          arrivedClean: 'No',
          confirmed: 'approved',
          approvalDetails: 'Per Customer',
        },
        cleaningQA: {
          commodityStencilCovered: 'Yes',
          exteriorInspectionPass: 'Yes',
          interiorInspectionPass: 'Yes',
          NiApplied: 'Yes',
          orangeTagApplied: 'Yes',
          placardsRemoved: 'Yes',
        },
      }
    }

    it('parses data successfully', () => {
      const input = data()

      expect(CleaningOtherResponse.parse(input)).toEqual(input)
    })

    describe('cleaningInspection', () => {
      it.each([
        'measurement',
        'ventOnly',
        'exteriorCleanRequired',
        'depthOfCommodity',
        'totalGallons',
      ] as const)('fails to parse when %s is missing', (key) => {
        const input = data()

        delete input.cleaningInspection[
          key as keyof typeof input.cleaningInspection
        ]

        expect(CleaningOtherResponse.safeParse(input).success).toBeFalsy()
      })
    })

    describe('cleaningApproval', () => {
      describe('when car arrived clean', () => {
        it('parses successfully when cleaning is approved', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'approved',
            approvalDetails: 'Per Customer',
          }

          const output = data()

          output.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'approved',
            approvalDetails: 'Per Customer',
          }

          expect(CleaningOtherResponse.parse(input)).toEqual(output)
        })

        it('parses successfully when cleaning is rejected', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'rejected',
            rejectionDetails: 'Handle DNO/DNE',
          }

          const output = data()

          output.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'rejected',
            rejectionDetails: 'Handle DNO/DNE',
          }

          output.cleaningInformation = null
          output.cleaningQA = null

          expect(CleaningOtherResponse.parse(input)).toEqual(output)
        })

        it('parses successfully when cleaning is not required', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'not-required',
          }

          input.cleaningInformation = {
            cleanedPer: 'gatx_recipe',
            cleaningPerformed: ['Car is Clean - Vent Only'],
            cleaningSpecType: 'reviewed',
            totalGalRemoved: 1,
            drumLocation: 'Back',

            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            cleaningMethods: ['Nitrogen'] as unknown as null,
          }

          const output = data()

          output.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'not-required',
          }

          output.cleaningInformation = {
            cleaningPerformed: ['Car is Clean - Vent Only'],
            cleaningSpecType: 'reviewed',
            totalGalRemoved: 1,
            drumLocation: 'Back',
            totalFlareHours: null,
          }

          expect(CleaningOtherResponse.parse(input)).toEqual(output)
        })
      })

      describe('when car did not arrive clean', () => {
        it('parses successfully when cleaning is approved', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'No',
            confirmed: 'approved',
            approvalDetails: 'Per Customer',
          }

          const output = data()

          output.cleaningApproval = {
            arrivedClean: 'No',
            confirmed: 'approved',
            approvalDetails: 'Per Customer',
          }

          expect(CleaningOtherResponse.parse(input)).toEqual(output)
        })

        it('parses successfully when cleaning is rejected', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'No',
            confirmed: 'rejected',
            rejectionDetails: 'Handle DNO/DNE',
          }

          const output = data()

          output.cleaningApproval = {
            arrivedClean: 'No',
            confirmed: 'rejected',
            rejectionDetails: 'Handle DNO/DNE',
          }

          output.cleaningInformation = null
          output.cleaningQA = null

          expect(CleaningOtherResponse.parse(input)).toEqual(output)
        })

        it('fails to parse when cleaning is not required', () => {
          const input = data()

          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          input.cleaningApproval = {
            arrivedClean: 'No',
            confirmed: 'not-required',
          }

          expect(CleaningOtherResponse.safeParse(input).success).toBeFalsy()
        })
      })

      describe('when cleaning was approved', () => {
        it('fails to parse when approvalDetails is missing', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'approved',
            approvalDetails: 'Per Customer',
          }

          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          delete input.cleaningApproval.approvalDetails

          expect(CleaningOtherResponse.safeParse(input).success).toBeFalsy()
        })

        it('fails to parse when cleanedPer is missing', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'approved',
            approvalDetails: 'Per Customer',
          }

          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          delete input.cleaningInformation.cleanedPer

          expect(CleaningOtherResponse.safeParse(input).success).toBeFalsy()
        })

        it('removes cleaningInformation.cleaningPerformed', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'approved',
            approvalDetails: 'Per Customer',
          }

          input.cleaningInformation = {
            cleanedPer: 'gatx_recipe',
            cleaningMethods: ['Nitrogen'],
            cleaningSpecType: 'reviewed',
            totalGalRemoved: 1,
            drumLocation: 'Back',
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            cleaningPerformed: ['Car is Clean - Vent Only'],
          }

          const output = data()

          output.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'approved',
            approvalDetails: 'Per Customer',
          }

          output.cleaningInformation = {
            cleanedPer: 'gatx_recipe',
            cleaningMethods: ['Nitrogen'],
            cleaningSpecType: 'reviewed',
            totalGalRemoved: 1,
            drumLocation: 'Back',
          }

          const result = CleaningOtherResponse.parse(input)

          expect(result.cleaningInformation).not.toHaveProperty(
            'cleaningPerformed',
          )
        })
      })

      describe('when cleaning was rejected', () => {
        it('fails to parse when rejectionDetails is missing', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'rejected',
            rejectionDetails: 'Handle DNO/DNE',
          }

          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          delete input.cleaningApproval.rejectionDetails

          expect(CleaningOtherResponse.safeParse(input).success).toBeFalsy()
        })

        it('sets cleaningInformation and cleaningQA to null', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'rejected',
            rejectionDetails: 'Handle DNO/DNE',
          }

          const output = data()

          output.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'rejected',
            rejectionDetails: 'Handle DNO/DNE',
          }

          output.cleaningInformation = null
          output.cleaningQA = null

          expect(CleaningOtherResponse.parse(input)).toEqual(output)
        })
      })

      describe('when cleaning was not required', () => {
        it('removes cleaningInformation.cleaningMethods and cleaningInformation.cleanedPer', () => {
          const input = data()

          input.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'not-required',
          }

          input.cleaningInformation = {
            cleanedPer: 'gatx_recipe',
            cleaningMethods: ['Nitrogen'],
            cleaningSpecType: 'reviewed',
            totalGalRemoved: 1,
            drumLocation: 'Back',
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            cleaningPerformed: ['Car is Clean - Vent Only'],
          }

          const output = data()

          output.cleaningApproval = {
            arrivedClean: 'Yes',
            confirmed: 'not-required',
          }

          output.cleaningInformation = {
            cleaningSpecType: 'reviewed',
            totalGalRemoved: 1,
            drumLocation: 'Back',
            cleaningPerformed: ['Car is Clean - Vent Only'],
            totalFlareHours: null,
          }

          expect(CleaningOtherResponse.parse(input)).toEqual(output)
        })
      })
    })

    describe('cleaningInformation', () => {
      it.each(['cleaningSpecType', 'totalGalRemoved'] as const)(
        'fails to parse when %s is missing',
        (key) => {
          const input = data()

          delete input.cleaningInformation![
            key as keyof typeof input.cleaningInformation
          ]

          expect(CleaningOtherResponse.safeParse(input).success).toBeFalsy()
        },
      )
    })

    describe('cleaningQA', () => {
      it.each([
        'commodityStencilCovered',
        'exteriorInspectionPass',
        'interiorInspectionPass',
        'NiApplied',
        'orangeTagApplied',
        'placardsRemoved',
      ] as const)('fails to parse when %s is missing', (key) => {
        const input = data()

        delete input.cleaningQA![key as keyof typeof input.cleaningQA]

        expect(CleaningOtherResponse.safeParse(input).success).toBeFalsy()
      })
    })
  })

  describe.each(['film', 'N/A'] as const)(
    'when measurement is "%s"',
    (measurement) => {
      function data(): CleaningOtherResponse {
        return {
          cleaningInspection: {
            measurement,
            exteriorCleanRequired: 'Yes',
            ventOnly: 'Yes',
          },
          cleaningInformation: {
            cleanedPer: 'gatx_recipe',
            cleaningMethods: ['Nitrogen'],
            cleaningSpecType: 'reviewed',
          },
          cleaningApproval: {
            arrivedClean: 'No',
            confirmed: 'approved',
            approvalDetails: 'Per Customer',
          },
          cleaningQA: {
            commodityStencilCovered: 'Yes',
            exteriorInspectionPass: 'Yes',
            interiorInspectionPass: 'Yes',
            NiApplied: 'Yes',
            orangeTagApplied: 'Yes',
            placardsRemoved: 'Yes',
          },
        }
      }

      it('parses data successfully', () => {
        const input = data()

        expect(CleaningOtherResponse.parse(input)).toEqual(input)
      })

      describe('cleaningInspection', () => {
        it.each(['measurement', 'ventOnly', 'exteriorCleanRequired'] as const)(
          'fails to parse when %s is missing',
          (key) => {
            const input = data()

            delete input.cleaningInspection[key]

            expect(CleaningOtherResponse.safeParse(input).success).toBeFalsy()
          },
        )

        it.each(['depthOfCommodity', 'totalGallons'] as const)(
          'removes %s',
          (key) => {
            const input = data()

            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            input.cleaningInspection[key] = 1

            const output = data()

            expect(CleaningOtherResponse.parse(input)).toEqual(output)
          },
        )
      })

      describe('cleaningInformation', () => {
        it('fails to parse when cleaningSpecType is missing', () => {
          const input = data()

          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          delete input.cleaningInformation.cleaningSpecType

          expect(CleaningOtherResponse.safeParse(input).success).toBeFalsy()
        })

        it.each(['totalGalRemoved', 'drumLocation'])('removes %s', (key) => {
          const input = data()

          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          input.cleaningInformation![
            key as keyof typeof input.cleaningInformation
          ] = 1

          const output = data()

          expect(CleaningOtherResponse.parse(input)).toEqual(output)
        })
      })
    },
  )
})
