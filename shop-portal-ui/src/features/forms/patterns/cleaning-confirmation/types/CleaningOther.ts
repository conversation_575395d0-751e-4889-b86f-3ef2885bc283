import { z } from 'zod'

import { merge } from '@/features/forms/types/FormBranch'
import { CleaningConfirmationSectionId } from '../../../templates/CleaningConfirmation/types/CleaningConfirmation'
import { WithCleaningInspection } from './sections/CleaningInspection'
import { CommonCleaningQA } from './sections/CleaningQA'

const CleaningOtherResponse = merge(
  z.object({
    cleaningQA: CommonCleaningQA,
  }),
  WithCleaningInspection,
)

type CleaningOtherResponse = z.infer<typeof CleaningOtherResponse>

export {
  CleaningConfirmationSectionId as CleaningFormSectionId,
  CleaningOtherResponse,
}
