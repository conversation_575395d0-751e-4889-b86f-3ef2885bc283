import { useFormSignatures } from '@/features/forms/providers/FormProvider'
import { useFormQuestionResponse } from '@/features/forms/providers/FormQuestionProvider'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Accordion } from '@gatx-corp/platform-one-common/components/Accordion'
import { FC } from 'react'
import { CleaningConfirmationSectionId } from '../../../templates/CleaningConfirmation/types/CleaningConfirmation'
import { CleaningGeneralResponse } from '../types/CleaningGeneral'
import FormSection from './FormSection'
import CleaningApprovalSection from './sections/CleaningApprovalSection'
import CleaningInformationSection from './sections/CleaningInformationSection'
import CleaningInspectionSection from './sections/CleaningInspectionSection'
import CleaningQASection from './sections/CleaningQASection'

const CleaningGeneral: FC = () => {
  const { t } = useI18n('Forms', { keyPrefix: 'CleaningGeneral' })
  const [response] = useFormQuestionResponse<Partial<CleaningGeneralResponse>>()
  const { completed } = useFormSignatures()

  const cleaningInspectionSigned = completed(
    CleaningConfirmationSectionId.CLEANING_INSPECTION,
  )
  const cleaningApprovalSigned = completed(
    CleaningConfirmationSectionId.CLEANING_APPROVAL,
  )
  const cleaningInformationSigned = completed(
    CleaningConfirmationSectionId.CLEANING_INFORMATION,
  )

  const cleaningRejected = response.cleaningApproval?.confirmed === 'rejected'
  const cleaningQASigned = completed(CleaningConfirmationSectionId.CLEANING_QA)
  const renderPostApprovalSections =
    !cleaningApprovalSigned || !cleaningRejected

  return (
    <Accordion.Container
      variant="card"
      hasExpandCollapse
      screenReaderContext={t('cleaningGeneralTitle')}
    >
      <FormSection
        id={CleaningConfirmationSectionId.CLEANING_INSPECTION}
        unsignable={!cleaningApprovalSigned}
        title={t('cleaningInspectionInformationTitle')}
        signatureLabel={t('cleaningInspectionSignatureLabel')}
        signatureAcknowledgment={t('cleaningInspectionSignatureAck')}
      >
        <CleaningInspectionSection />
      </FormSection>
      <FormSection
        id={CleaningConfirmationSectionId.CLEANING_APPROVAL}
        disabled={!cleaningInspectionSigned}
        unsignable={!cleaningInformationSigned}
        title={t('cleaningApprovalTitle')}
        signatureLabel={t('cleaningApprovalSignatureLabel')}
        signatureAcknowledgment={t('cleaningApprovalSignatureAck')}
        className="customer-print:hidden"
        isFinal={cleaningRejected}
      >
        <CleaningApprovalSection />
      </FormSection>
      <FormSection
        disabled={!cleaningApprovalSigned}
        unsignable={!cleaningQASigned}
        id={CleaningConfirmationSectionId.CLEANING_INFORMATION}
        title={t('cleaningInformationTitle')}
        signatureLabel={t('cleaningInformationSignatureLabel')}
        signatureAcknowledgment={t('cleaningInformationSignatureAck')}
        hidden={!renderPostApprovalSections}
      >
        <CleaningInformationSection />
      </FormSection>
      <FormSection
        disabled={!cleaningInformationSigned}
        id={CleaningConfirmationSectionId.CLEANING_QA}
        title={t('cleaningQATitle')}
        signatureLabel={t('cleaningQASignatureLabel')}
        signatureAcknowledgment={t('cleaningQASignatureAck')}
        hidden={!renderPostApprovalSections}
        isFinal={!cleaningRejected}
      >
        <CleaningQASection />
      </FormSection>
    </Accordion.Container>
  )
}

export default CleaningGeneral
