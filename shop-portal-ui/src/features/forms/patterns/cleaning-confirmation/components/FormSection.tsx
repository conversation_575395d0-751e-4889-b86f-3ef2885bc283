import { useI18n } from '@gatx-corp/platform-one-common'
import { Accordion } from '@gatx-corp/platform-one-common/components/Accordion'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import clsx from 'clsx'
import { useState } from 'react'
import FormSignatureInput, {
  FormSignatureBadge,
  SignatureError,
} from '../../../components/FormSignatureInput'
import { CleaningConfirmationSectionId } from '../../../templates/CleaningConfirmation/types/CleaningConfirmation'
import { useValidateFormSection } from '../hooks/useValidateFormSection'

type Props = {
  id: string
  title: string
  signatureLabel: string
  signatureAcknowledgment: string
  children?: React.ReactNode
  disabled?: boolean
  unsignable?: boolean
  isFinal?: boolean
  hidden?: boolean
  className?: string
}

const FormSection = ({
  id,
  title,
  signatureLabel,
  signatureAcknowledgment,
  children,
  disabled = false,
  unsignable = true,
  isFinal = false,
  hidden,
  className,
}: Props) => {
  const { t } = useI18n('Forms')
  const validate = useValidateFormSection()

  const [error, setError] = useState<SignatureError | undefined>()

  return (
    <Accordion.Item
      title={title}
      indicators={
        <div className="contents customer-print:flex gap-lg items-center">
          {id ===
            (CleaningConfirmationSectionId.CLEANING_INSPECTION as string) && (
            <span className="hidden customer-print:block text-body-sm">
              {t('procedureTBD')}
            </span>
          )}
          <FormSignatureBadge
            id={id}
            label={signatureLabel}
            hidden={disabled}
          />
        </div>
      }
      disabled={disabled}
      className={clsx('print:block', className, { hidden })}
    >
      <div className="mx-md mt-sm" hidden={!error}>
        {error && (
          <Alert
            title={error.status ? `Error [${error.status}]` : 'Error'}
            urgency="immediate"
            level="error"
            compact
          >
            {error.message}
          </Alert>
        )}
      </div>
      <div className="px-md pt-md pb-0 flex flex-col print:p-0">
        {children}
        <hr className="border-b h-0 border-light-6 dark:border-dark-5 my-lg print:hidden" />
        <FormSignatureInput
          id={id}
          label={signatureLabel}
          acknowledgment={signatureAcknowledgment}
          signable={!disabled && validate(id)}
          unsignable={unsignable}
          onError={setError}
          isFinal={isFinal}
        />
      </div>
    </Accordion.Item>
  )
}

export default FormSection
