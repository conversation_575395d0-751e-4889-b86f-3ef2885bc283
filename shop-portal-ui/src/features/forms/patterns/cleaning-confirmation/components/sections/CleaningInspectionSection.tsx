import DataField from '@/features/forms/components/DataField'
import FormC<PERSON>iceGroupField from '@/features/forms/components/FormChoiceGroupField'
import FormNumberField from '@/features/forms/components/FormNumberField'
import FormTextField from '@/features/forms/components/FormTextField'
import { useI18n } from '@gatx-corp/platform-one-common'
import { z } from 'zod'
import { CleaningConfirmationSectionId } from '../../../../templates/CleaningConfirmation/types/CleaningConfirmation'
import { useFormSectionFieldProps } from '../../hooks/useFormSectionFieldProps'
import QuantitativeGasTestingReference from '../QuantitativeGasTestingReference'

const ID = CleaningConfirmationSectionId.CLEANING_INSPECTION

const CleaningInspectionSection = () => {
  const { t } = useI18n('Forms', {
    keyPrefix: 'Cleaning Confirmation.cleaningInspection',
  })

  const fields = useFormSectionFieldProps(ID)

  return (
    <>
      <div className="flex flex-col print:gap-sm customer-print:hidden">
        <div className="flex flex-wrap gap-xl print:gap-x-xl print:gap-y-xs">
          {fields.measurement && (
            <FormChoiceGroupField size="large" {...fields.measurement} />
          )}

          <FormTextField
            /* To be implemented in S-17568 */
            label="Procedure"
            value="TBD"
            onChange={() => undefined}
            readOnly
          />
        </div>
        <hr className="border-b h-0 border-light-6 dark:border-dark-5 my-lg print:hidden" />
        <div className="flex flex-wrap gap-xl print:gap-x-xl print:gap-y-xs">
          {fields.depthOfCommodity && (
            <FormNumberField {...fields.depthOfCommodity} />
          )}
          {fields.totalGallons && <FormNumberField {...fields.totalGallons} />}

          {fields.pressure && <FormNumberField {...fields.pressure} />}
          {fields.solidsPresent && (
            <FormChoiceGroupField size="large" {...fields.solidsPresent} />
          )}
          {fields.pH && <FormNumberField {...fields.pH} />}
          {fields.temperature && <FormNumberField {...fields.temperature} />}
          {fields.ambientTemperature && (
            <FormNumberField {...fields.ambientTemperature} />
          )}
          {fields.ventOnly && (
            <FormChoiceGroupField size="large" {...fields.ventOnly} />
          )}
          {fields.exteriorCleanRequired && (
            <FormChoiceGroupField
              size="large"
              {...fields.exteriorCleanRequired}
            />
          )}
          {fields.HPInCar && (
            <FormChoiceGroupField size="large" {...fields.HPInCar} />
          )}
          {fields.QGtest && (
            <FormChoiceGroupField size="large" {...fields.QGtest} />
          )}
          {fields.QGTtubeUsed && <FormTextField {...fields.QGTtubeUsed} />}
          {fields.QGTppm && <FormNumberField {...fields.QGTppm} />}
        </div>
        {fields.QGtest && <QuantitativeGasTestingReference />}
      </div>
      <dl
        className="hidden customer-print:flex capitalize gap-lg bg-light-3 rounded py-xs px-sm"
        aria-hidden
      >
        <DataField label={t('pressure')}>
          {z.coerce.string().default('—').parse(fields.pressure?.value)}
        </DataField>

        <DataField label={t('temperature')}>
          {z.coerce.string().default('—').parse(fields.temperature?.value)}
        </DataField>

        <DataField label={t('ventOnly')}>
          {z.string().default('—').parse(fields.ventOnly?.value)}
        </DataField>

        <DataField label={t('exteriorCleanRequired')}>
          {z.string().default('—').parse(fields.exteriorCleanRequired?.value)}
        </DataField>

        <DataField label={t('HPInCar')}>
          {z.string().default('—').parse(fields.HPInCar?.value)}
        </DataField>
      </dl>
    </>
  )
}

export default CleaningInspectionSection
