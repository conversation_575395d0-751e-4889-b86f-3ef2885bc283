import { z } from 'zod'

import DataField from '@/features/forms/components/DataField'
import FormChoiceGroupField from '@/features/forms/components/FormChoiceGroupField'
import FormNumberField from '@/features/forms/components/FormNumberField'
import FormTextField from '@/features/forms/components/FormTextField'
import { useFormServiceEvent } from '@/features/forms/providers/FormProvider'
import { useI18n } from '@gatx-corp/platform-one-common'
import { CleaningConfirmationSectionId } from '../../../../templates/CleaningConfirmation/types/CleaningConfirmation'
import { useFormSectionFieldProps } from '../../hooks/useFormSectionFieldProps'

const ID = CleaningConfirmationSectionId.CLEANING_INFORMATION

export const CleaningInformationSection = () => {
  const serviceEvent = useFormServiceEvent()

  const fields = useFormSectionFieldProps(ID, {
    metadata: {
      recipeNumber: serviceEvent?.commodityData.confirmed.cinNumber,
    },
  })

  const { t } = useI18n('Forms', {
    keyPrefix: 'Cleaning Confirmation.cleaningInformation',
  })

  return (
    <>
      <div className="flex flex-col print:gap-sm customer-print:hidden">
        {fields.cleaningSpecType && (
          <FormChoiceGroupField
            variant="classic"
            labelScreenreaderOnly
            layout="vertical"
            {...fields.cleaningSpecType}
          />
        )}
        <hr className="border-b h-0 border-light-6 dark:border-dark-5 print:hidden my-lg" />

        <div className="text-body-sm text-dark-5 dark:text-dark-2">
          <p>{t('entryProceduresMessageParagraph1')}</p>
          <p>{t('entryProceduresMessageParagraph2')}</p>
        </div>

        <div className="print:hidden flex flex-wrap my-lg gap-xl">
          {fields.cleanedPer && (
            <FormChoiceGroupField size="large" {...fields.cleanedPer} />
          )}

          {fields.cleaningMethods && (
            <FormChoiceGroupField size="large" {...fields.cleaningMethods} />
          )}
        </div>

        {fields.cleanedPer && fields.cleaningMethods && (
          <div className="hidden print:block" aria-hidden>
            <FormChoiceGroupField
              label={t('cleaningMethods')}
              labelScreenreaderOnly
              size="large"
              value={[
                fields.cleanedPer.value ?? '',
                fields.cleaningMethods.value ?? '',
              ].flat()}
              onChange={() => undefined}
              options={[
                fields.cleanedPer.options ?? [],
                fields.cleaningMethods.options ?? [],
              ].flat()}
              printOptions={[
                fields.cleanedPer.printOptions ?? [],
                fields.cleaningMethods.printOptions ?? [],
              ].flat()}
            />
          </div>
        )}

        {fields.cleaningPerformed && (
          <div className="print:block">
            <FormChoiceGroupField size="large" {...fields.cleaningPerformed} />
          </div>
        )}

        <div className="flex flex-wrap my-lg print:my-xs gap-xl print:gap-y-xs">
          {fields.totalGalRemoved && (
            <FormNumberField {...fields.totalGalRemoved} />
          )}
          {fields.drumLocation && <FormTextField {...fields.drumLocation} />}
          {fields.totalFlareHours && (
            <FormNumberField {...fields.totalFlareHours} />
          )}
        </div>
      </div>
      <dl
        className="hidden customer-print:flex capitalize gap-lg bg-light-3 rounded py-xs px-sm"
        aria-hidden
      >
        <DataField label={t('cleaningMethodsLabel')}>
          {z
            .string()
            .array()
            .default(['—'])
            .parse(fields.cleaningMethods?.value)
            .join(', ')}
        </DataField>
      </dl>
    </>
  )
}

export default CleaningInformationSection
