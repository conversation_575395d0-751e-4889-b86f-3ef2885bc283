import { _setupCachingForTests } from '@/features/caching/providers'
import {
  _ClientDBRxDatabase,
  _setupClientDBForTests,
  _setupRxDB,
} from '@/features/client-db/providers'
import { CleaningGeneralResponse } from '@/features/forms/patterns/cleaning-confirmation/types/CleaningGeneral'
import * as formQuestionHooks from '@/features/forms/providers/FormQuestionProvider'
import { resolveSchema } from '@/features/forms/utils/schema'
import { Json } from '@/types/Json'
import { renderHook } from '@testing-library/react'
import { ZodSchema } from 'zod'
import { useValidateFormSection } from './useValidateFormSection'

jest.mock('@/features/forms/api/getListOptions', () => ({
  getListOptions: jest.fn().mockResolvedValue({
    success: true,
    response: [
      { id: 1, value: 'Nitrogen', label: 'Nitrogen', metadata: {}, list: '' },
    ],
  }),
}))

jest.mock('@/features/forms/providers/FormQuestionProvider', () => ({
  useFormQuestionResponse: jest.fn(),
  usePatternSchema: jest.fn(),
}))

async function mockPatternSchema(Schema: ZodSchema) {
  _setupClientDBForTests(await _setupRxDB(true))
  _setupCachingForTests()
  await resolveSchema(Schema)
  jest.spyOn(formQuestionHooks, 'usePatternSchema').mockReturnValue(Schema)
}

function mockQuestionResponse(response: Record<string, Json | undefined>) {
  jest
    .spyOn(formQuestionHooks, 'useFormQuestionResponse')
    .mockReturnValue([response, jest.fn()])
}

function useHook() {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  return renderHook(() => useValidateFormSection()).result.current
}

type TestPresenceOpts<S extends string, K extends string> = {
  sectionId: S
  fixture: () => Record<S, Record<K, Json>>
  required: boolean
}

function testPresence<S extends string, K extends string>(
  keys: K[],
  { sectionId, fixture, required }: TestPresenceOpts<S, K>,
) {
  it.each(keys)(
    `${!required ? 'is' : 'is not'} valid if the section is missing %s`,
    (key) => {
      const response = fixture()

      delete response[sectionId][key]

      mockQuestionResponse(response)

      const validate = useHook()

      expect(validate(sectionId)).toBe(!required)
    },
  )
}

function testRequired<S extends string, K extends string>(
  keys: K[],
  opts: Omit<TestPresenceOpts<S, K>, 'required'>,
) {
  testPresence(keys, { ...opts, required: true })
}

function testNotRequired<S extends string, K extends string>(
  keys: K[],
  opts: Omit<TestPresenceOpts<S, K>, 'required'>,
) {
  testPresence(keys, { ...opts, required: false })
}

describe('useValidateFormSection on CleaningGeneralResponse', () => {
  beforeAll(async () => {
    await mockPatternSchema(CleaningGeneralResponse)
  })

  afterAll(async () => {
    await _ClientDBRxDatabase?.destroy()
    jest.clearAllMocks()
  })

  describe('cleaningInspection with "inches" measurement', () => {
    function fixture() {
      return {
        cleaningInspection: {
          measurement: 'inches',
          depthOfCommodity: 1.4,
          totalGallons: 100,
          exteriorCleanRequired: 'Yes',
          ventOnly: 'No',
          pH: 7,
          solidsPresent: 'Yes',
        } as Record<string, Json>,
      }
    }

    it('is valid if all required fields are provided', () => {
      mockQuestionResponse(fixture())

      const validate = useHook()

      expect(validate('cleaningInspection')).toBe(true)
    })

    testRequired(
      [
        'totalGallons',
        'depthOfCommodity',
        'solidsPresent',
        'ventOnly',
        'exteriorCleanRequired',
      ],
      {
        sectionId: 'cleaningInspection',
        fixture,
      },
    )

    testNotRequired(['pH'], {
      sectionId: 'cleaningInspection',
      fixture,
    })
  })

  describe.each(['film', 'N/A'])(
    'cleaningInspection with "%s" measurement',
    (measurement) => {
      function fixture() {
        return {
          cleaningInspection: {
            measurement,
            depthOfCommodity: 1.4,
            totalGallons: 100,
            exteriorCleanRequired: 'Yes',
            ventOnly: 'No',
            pH: 7,
            solidsPresent: 'Yes',
          } as Record<string, Json>,
        }
      }

      it('is valid if all required fields are provided', () => {
        mockQuestionResponse(fixture())

        const validate = useHook()

        expect(validate('cleaningInspection')).toBe(true)
      })

      testRequired(['solidsPresent', 'ventOnly', 'exteriorCleanRequired'], {
        sectionId: 'cleaningInspection',
        fixture,
      })

      testNotRequired(['pH', 'totalGallons', 'depthOfCommodity'], {
        sectionId: 'cleaningInspection',
        fixture,
      })
    },
  )

  describe.each(['Yes', 'No'])(
    'cleaningApproval when arrivedClean is "%s" and confirmed is "approved"',
    (arrivedClean) => {
      function fixture() {
        return {
          cleaningApproval: {
            arrivedClean,
            confirmed: 'approved',
            approvalDetails: 'Per EGP-05 Guideline',
            rejectionDetails: 'Handle DNO/DNE',
          },
        }
      }

      it('is valid if all required fields are provided', () => {
        mockQuestionResponse(fixture())

        const validate = useHook()

        expect(validate('cleaningApproval')).toBe(true)
      })

      testRequired(['approvalDetails'], {
        sectionId: 'cleaningApproval',
        fixture,
      })

      testNotRequired(['rejectionDetails'], {
        sectionId: 'cleaningApproval',
        fixture,
      })
    },
  )

  describe.each(['Yes', 'No'])(
    'cleaningApproval when arrivedClean is "%s" and confirmed is "rejected"',
    (arrivedClean) => {
      function fixture() {
        return {
          cleaningApproval: {
            arrivedClean,
            confirmed: 'rejected',
            approvalDetails: 'Per EGP-05 Guideline',
            rejectionDetails: 'Handle DNO/DNE',
          },
        }
      }

      it('is valid if all required fields are provided', () => {
        mockQuestionResponse(fixture())

        const validate = useHook()

        expect(validate('cleaningApproval')).toBe(true)
      })

      testRequired(['rejectionDetails'], {
        sectionId: 'cleaningApproval',
        fixture,
      })

      testNotRequired(['approvalDetails'], {
        sectionId: 'cleaningApproval',
        fixture,
      })
    },
  )

  describe('cleaningApproval when arrivedClean is "No" and confirmed is "not-required"', () => {
    function fixture() {
      return {
        cleaningApproval: {
          arrivedClean: 'No',
          confirmed: 'not-required',
          approvalDetails: 'Per EGP-05 Guideline',
          rejectionDetails: 'Handle DNO/DNE',
        },
      }
    }

    it('is not valid', () => {
      mockQuestionResponse(fixture())

      const validate = useHook()

      expect(validate('cleaningApproval')).toBe(false)
    })
  })

  describe('cleaningApproval when arrivedClean is "Yes" and confirmed is "not-required"', () => {
    function fixture() {
      return {
        cleaningApproval: {
          arrivedClean: 'Yes',
          confirmed: 'not-required',
          approvalDetails: 'Per EGP-05 Guideline',
          rejectionDetails: 'Handle DNO/DNE',
        },
      }
    }

    it('is valid', () => {
      mockQuestionResponse(fixture())

      const validate = useHook()

      expect(validate('cleaningApproval')).toBe(true)
    })
  })

  describe('cleaningInformation when measurement is "inches"', () => {
    function fixture() {
      return {
        cleaningInspection: {
          measurement: 'inches',
        },
        cleaningInformation: {
          cleanedPer: 'gatx_recipe',
          cleaningMethods: ['Nitrogen'],
          totalGalRemoved: 100,
          totalFlareHours: 10,
          cleaningPerformed: ['Car is Clean - Vent Only'],
          cleaningSpecType: 'reviewed',
          drumLocation: 'Drum 1',
        },
      }
    }

    it('is valid if all required fields are provided', () => {
      mockQuestionResponse(fixture())

      const validate = useHook()

      expect(validate('cleaningInformation')).toBe(true)
    })

    testRequired(['cleanedPer', 'totalGalRemoved', 'cleaningSpecType'], {
      sectionId: 'cleaningInformation',
      fixture,
    })

    testNotRequired(
      [
        'cleaningMethods',
        'totalFlareHours',
        'cleaningPerformed',
        'drumLocation',
      ],
      {
        sectionId: 'cleaningInformation',
        fixture,
      },
    )
  })

  describe.each(['film', 'N/A'])(
    'cleaningInformation with "%s" measurement',
    (measurement) => {
      function fixture() {
        return {
          cleaningInspection: {
            measurement,
          },
          cleaningInformation: {
            cleanedPer: 'gatx_recipe',
            cleaningMethods: ['Nitrogen'],
            totalGalRemoved: 100,
            totalFlareHours: 10,
            cleaningPerformed: ['Car is Clean - Vent Only'],
            cleaningSpecType: 'reviewed',
            drumLocation: 'Drum 1',
          },
        }
      }

      it('is valid if all required fields are provided', () => {
        mockQuestionResponse(fixture())

        const validate = useHook()

        expect(validate('cleaningInformation')).toBe(true)
      })

      testRequired(['cleanedPer', 'cleaningSpecType'], {
        sectionId: 'cleaningInformation',
        fixture,
      })

      testNotRequired(
        [
          'cleaningMethods',
          'totalGalRemoved',
          'totalFlareHours',
          'cleaningPerformed',
          'drumLocation',
        ],
        {
          sectionId: 'cleaningInformation',
          fixture,
        },
      )
    },
  )

  describe('cleaningQA', () => {
    function fixture() {
      return {
        cleaningQA: {
          interiorInspectionPass: 'Yes',
          exteriorInspectionPass: 'No',
          placardsRemoved: 'Yes',
          commodityStencilCovered: 'N/A',
          orangeTagApplied: 'Yes',
          NiApplied: 'Yes',
        },
      }
    }

    it('is valid if all required fields are provided', () => {
      mockQuestionResponse(fixture())

      const validate = useHook()

      expect(validate('cleaningQA')).toBe(true)
    })

    testRequired(
      [
        'interiorInspectionPass',
        'exteriorInspectionPass',
        'placardsRemoved',
        'commodityStencilCovered',
        'commodityStencilCovered',
        'orangeTagApplied',
        'NiApplied',
      ],
      {
        sectionId: 'cleaningQA',
        fixture,
      },
    )
  })
})
