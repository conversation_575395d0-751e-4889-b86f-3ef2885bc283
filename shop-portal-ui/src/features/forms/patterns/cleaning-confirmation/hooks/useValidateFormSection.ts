import {
  useFormQuestionResponse,
  usePatternSchema,
} from '@/features/forms/providers/FormQuestionProvider'
import { isPropertyValid } from '@/features/forms/utils/schema'

export function useValidateFormSection(): (sectionId: string) => boolean {
  const Schema = usePatternSchema()
  const [response] = useFormQuestionResponse()

  return (sectionId: string) =>
    !!Schema && isPropertyValid(Schema, [sectionId], response)
}
