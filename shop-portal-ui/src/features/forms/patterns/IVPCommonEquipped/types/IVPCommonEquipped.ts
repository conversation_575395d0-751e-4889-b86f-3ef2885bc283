import { getOptions } from '@/features/forms/api/getOptions'
import branch, { merge } from '@/features/forms/types/FormBranch'
import options, { No, Yes } from '@/features/forms/types/FormOptions'
import { HAS_MR_DATA } from '@/features/forms/types/FormSchemaConstants'
import { z } from 'zod'
import { WithMRData, WithNoMRData } from '../../types/MRData'

const CurrentDataOptions = options().async(
  async (query) => await getOptions(query),
)

const IVPCommonEquipped = branch(
  [HAS_MR_DATA],
  WithNoMRData.extend({
    currentData: CurrentDataOptions,
  }),
  merge(
    WithMRData,
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes,
      }),
      merge(
        z.object({
          allCorrect: No,
        }),
        branch(
          ['currentData_reply'],
          z.object({
            currentData_reply: options("Can't Tell"),
          }),
          z.object({
            currentData_reply: options('Change'),
            currentData: CurrentDataOptions,
          }),
        ),
      ),
    ),
  ),
)
type IVPCommonEquipped = z.infer<typeof IVPCommonEquipped>

export { IVPCommonEquipped }
