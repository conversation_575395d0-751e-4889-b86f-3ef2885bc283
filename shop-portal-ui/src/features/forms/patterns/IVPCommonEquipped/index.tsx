import FormChoiceGroupField from '@/features/forms/components/FormChoiceGroupField'
import FormSelectField from '@/features/forms/components/FormSelectField'
import FormTextField from '@/features/forms/components/FormTextField'
import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { FALSE, NO, TRUE } from '../../types/FormSchemaConstants'

const IVPCommonEquipped = () => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ path: [], signable: true })

  return (
    <fieldset>
      {fields.hasMRData?.value === FALSE && (
        <>
          <Alert
            title={t('noMRData')}
            urgency="immediate"
            level="warning"
            compact
          >
            {t('noMRDataText')}
          </Alert>

          <div className="flex mt-xl">
            {fields.currentData && (
              <FormSelectField
                {...fields.currentData}
                label={t('IVPCommonEquipped.currentData_MR')}
              />
            )}
          </div>
        </>
      )}

      {fields.hasMRData?.value === TRUE && (
        <>
          {fields.allCorrect && (
            <FormChoiceGroupField size="large" {...fields.allCorrect} />
          )}

          <div className="flex gap-2xl mt-xl">
            <FormTextField
              readOnly
              value={
                fields.currentData_MR?.value
                  ? (fields.currentData_MR.value as string)
                  : t('none')
              }
              label={t('IVPCommonEquipped.currentData_MR')}
              onChange={() => undefined}
            />

            {fields.allCorrect?.value === NO && fields.currentData_reply && (
              <FormChoiceGroupField
                size="large"
                {...fields.currentData_reply}
              />
            )}

            {fields.currentData_reply?.value && fields.currentData && (
              <FormSelectField
                {...fields.currentData}
                label={t('IVPCommonEquipped.correction')}
              />
            )}
          </div>
        </>
      )}
    </fieldset>
  )
}

export default IVPCommonEquipped
