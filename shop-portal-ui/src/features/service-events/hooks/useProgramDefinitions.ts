import { useCachingQuery } from '@/features/caching/hooks/useCachingQuery'
import {
  CachingQueryResult,
  DataConfig,
  RequestOptions,
} from '@/features/caching/types'
import { DEFAULT_TTL_SECONDS } from '@/features/caching/utils/ttl'
import { loadProgramDefinitions } from '../api'
import { ProgramDefinition } from '../types/ProgramDefinition'

type Options = RequestOptions
/**
 * The config for the program-status-code data.
 */
const PROGRAM_DATA_CONFIG: DataConfig<ProgramDefinition[]> = {
  dataSetName: 'programDefinitions',
  remoteQueryFunction: loadProgramDefinitions,
  timeToLive: DEFAULT_TTL_SECONDS,
}

export function useProgramDefinitions(
  options: Options,
): CachingQueryResult<ProgramDefinition[]> {
  return useCachingQuery(
    options,
    encodeURIComponent(JSON.stringify({})),
    PROGRAM_DATA_CONFIG,
  )
}
