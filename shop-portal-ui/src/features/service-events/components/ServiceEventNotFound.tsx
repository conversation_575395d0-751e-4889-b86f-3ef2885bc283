import { Railcar } from '@/features/railcars/types/Railcar'
import { i18n } from '@gatx-corp/platform-one-common'
import { Heading } from '@gatx-corp/platform-one-common/components/Content'
import { NotFoundIcon } from '@gatx-corp/platform-one-common/icons/NotFound'
import { Trans } from 'react-i18next'

type Props = {
  railcar: Railcar
}

export const ServiceEventNotFound = ({ railcar }: Props) => {
  return (
    <div className="px-lg py-2xl flex flex-col items-center justify-center absolute inset-0">
      <NotFoundIcon className="size-[108px] mx-auto mb-2xl" aria-hidden />

      <Trans
        i18n={i18n}
        i18nKey="NotFound"
        ns="ServiceEvents"
        components={{
          title: <Heading className="text-title-rg mb-md" />,
          description: <p className="text-subtle text-body-rg" />,
        }}
        values={{
          count: railcar.serviceEvents.length,
        }}
      />
    </div>
  )
}
