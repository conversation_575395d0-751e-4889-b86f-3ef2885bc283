{"serviceEvent": "Service Event", "NotFound_zero": "<title>No Service Events For This Car</title><p>Please contact your system administrator for support</p>", "NotFound_other": "<title>Service Event Not Found</title><p>Please contact your system administrator for support</p>", "NotFound_nav_zero": "No Service Events", "NotFound_nav_other": "Service Event Not Found", "NotFound.badge": "Event Not Found", "loading": "Loading Service Event...", "Commodities": {"confirmed": "Confirmed", "confirmedCommodity": "Confirmed Commodity", "futureCommodity": "Future Commodity", "corrosiveBadge": "Corrosive", "safetyInformation": "Safety Information", "sdsRequested": "SDS Requested", "carReportedClean": "Car Reported Clean", "restrictionCode": "Restriction Code", "cleaningCertInDB": "Cleaning Cert In DB", "viewCleaningCertificate": "View Cleaning Certificate"}, "Delivery": {"delivery": "Delivery", "contingencyReason": "Contingency reason", "contingencyComment": "Contingency comment", "disposition": "Disposition", "freight": "Freight", "pod": "POD", "podAlertSuccess": "On Track", "podAlertWarning": "Due Within 13 Days", "podAlertError": "Past Due", "podNotAvailable": "Not yet available"}, "Instructions": {"safetyAndQualityAlerts": "Safety & Quality Alerts", "noAlerts": "There are no alerts"}, "ServiceEvent": {"emailAtAddress": "Email {{address}}", "orderSummary": "Order Summary", "customerNumber": "Customer {{number}}", "aslName": "ASL Name", "aslPhone": "ASL Phone", "aslEmail": "ASL Email", "fomName": "FOM Name", "fomPhone": "FOM Phone", "fomEmail": "FOM Email", "leaseStatus": "Lease Status", "leaseType": "Lease Type", "nitrogenPadStage": "Nitrogen Pad Stage", "corrosiveMaterialWarning": "Warning: Commodity includes a corrosive material", "lastChanged": "Last changed {{date}}", "fleetMaintainanceAuditor": "Fleet Maintenance Auditor", "fleetMaintainanceEstimatingLead": "Fleet Maintenance Estimating Lead", "customer": "Customer", "commodity": "Commodity", "requiredPrograms": "Required Programs"}, "RequiredPrograms": {"programFormsIncomplete": "Program forms incomplete", "programFormsCompleted": "Program forms completed", "noAssociatedForms": "No associated forms", "programHeader": "Program", "programDescriptionHeader": "Program Description", "formStatusHeader": "Form Status", "dueHeader": "Due", "removedPrograms": "Removed Programs", "removed": "Removed", "programChanged.ADDED": "ADDED {{date}}", "programChanged.REMOVED": "REMOVED {{date}}"}, "ShoppingDetails": {"shoppingDetails": "Shopping Details", "rejectRepeatShopper": "Reject/Repeat Shopper", "movementApproval": "Movement Approval", "referenceDocMessage": "Perform {{referenceDoc}} per relevant question(s)"}}