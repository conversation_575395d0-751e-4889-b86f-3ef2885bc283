import { useCachingQuery } from '@/features/caching/hooks/useCachingQuery'
import {
  CachingQueryResult,
  DataConfig,
  RequestOptions,
} from '@/features/caching/types'
import { DEFAULT_TTL_SECONDS_CRITICAL } from '@/features/caching/utils/ttl'
import { getServiceEventComments } from '../api'
import { FormComment } from '../types/FormComment'

type Options = RequestOptions & {
  serviceEventId: string
}

/**
 * The config for the forms comments data.
 */
const FORM_COMMENTS_CONFIG: DataConfig<FormComment[]> = {
  dataSetName: 'serviceEventComments',
  remoteQueryFunction: getServiceEventComments,
  timeToLive: DEFAULT_TTL_SECONDS_CRITICAL,
}

export function useServiceEventComments(
  options: Options,
): CachingQueryResult<FormComment[]> {
  const { serviceEventId, isEnabled = true } = options
  options.isEnabled = !!serviceEventId && isEnabled

  return useCachingQuery(options, options.serviceEventId, FORM_COMMENTS_CONFIG)
}
