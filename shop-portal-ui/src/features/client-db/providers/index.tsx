'use client'

import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react'
import {
  addRxPlugin,
  createRxDatabase,
  removeRxDatabase,
  RxDatabase,
} from 'rxdb'
import { RxDBQueryBuilderPlugin } from 'rxdb/plugins/query-builder'
import { getRxStorageDexie } from 'rxdb/plugins/storage-dexie'
import { getRxStorageMemory } from 'rxdb/plugins/storage-memory'
import { _COLLECTIONS } from '../types/collections'

export type ClientDBContext = {
  database?: RxDatabase
}

export const ClientDBContext = createContext<ClientDBContext | undefined>(
  undefined,
)

/**
 * Called to set-up the database. Note that setting up the DB is not instant, so do not expect the Database to
 * be immediately available once this returns.
 *
 * @returns : The RxDb instance.
 */
const setupRxDB = async (inMemory = false): Promise<RxDatabase> => {
  /**
   * We allow duplicate instances in development because of the way NextJS does live reloading. However
   * this SHOULD be false in production
   */
  let ignoreDuplicate = false

  if (process.env.NODE_ENV !== 'production' && !inMemory) {
    ignoreDuplicate = true

    /**
     * Use dev-mode when appropriate. This increases the size of the bundle, but promises to
     * catch a lot of problems.
     *
     * For more information, see: https://rxdb.info/dev-mode.html
     */
    await import('rxdb/plugins/dev-mode').then((module) =>
      addRxPlugin(module.RxDBDevModePlugin),
    )
  }

  const createLiveDB = async (): Promise<RxDatabase> =>
    await createRxDatabase({
      name: 'caching-db',
      storage: getRxStorageDexie(),
      ignoreDuplicate,
      multiInstance: true,
    })

  const createInMemoryDB = async (): Promise<RxDatabase> => {
    console.info('Creating RxDB in-memory')
    return await createRxDatabase({
      name: 'caching-db',
      storage: getRxStorageMemory(),
      ignoreDuplicate: false,
      multiInstance: false,
    })
  }

  addRxPlugin(RxDBQueryBuilderPlugin)

  // Now, the actual DB.
  const cachingDB = await (inMemory ? createInMemoryDB() : createLiveDB())

  // Add the schemas.
  await cachingDB.addCollections(_COLLECTIONS)

  return cachingDB
}

/**
 * Called to shutdown the RxDB.
 */
const shutdownRxDB = async (rxDatabase?: RxDatabase) => {
  if (rxDatabase) {
    await removeRxDatabase(rxDatabase.name, rxDatabase.storage)
  }
}

let rxDatabase: RxDatabase | null = null

type Props = {
  withRxDatabase?: RxDatabase
  children: ReactNode
}

export const ClientDBProvider: React.FC<Props> = ({
  withRxDatabase,
  children,
}: Props) => {
  const [database, setDatabase] = useState<RxDatabase | undefined>(
    withRxDatabase,
  )

  rxDatabase = database ?? null

  useEffect(() => {
    if (!rxDatabase) {
      setupRxDB()
        .then((db) => {
          setDatabase(db)
        })
        .catch((error) => console.error('Unable to setup RxDB: ' + error))

      return () => {
        shutdownRxDB(database).catch(console.error)
      }
    }
  }, [database])

  return (
    <ClientDBContext.Provider value={{ database }}>
      {children}
    </ClientDBContext.Provider>
  )
}

export function _setupClientDBForTests(rxDb: RxDatabase) {
  rxDatabase = rxDb
}

export function useInClientDBContext(): boolean {
  return Boolean(useContext(ClientDBContext))
}

export {
  rxDatabase as _ClientDBRxDatabase,
  setupRxDB as _setupRxDB,
  shutdownRxDB as _shutdownRxDB,
}
